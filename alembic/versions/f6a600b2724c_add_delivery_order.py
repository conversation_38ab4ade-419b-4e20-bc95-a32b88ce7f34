"""add_delivery_order

Revision ID: f6a600b2724c
Revises: 2e7aaed3129f
Create Date: 2025-09-27 15:27:34.253484

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f6a600b2724c'
down_revision: Union[str, None] = '2e7aaed3129f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('delivery_orders',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('delivery_address_raw', sa.JSON(), nullable=True, comment='配送地址原始JSON信息'),
    sa.Column('delivery_address', sa.String(length=500), nullable=True, comment='配送地址字符串'),
    sa.Column('delivery_time_raw', sa.JSON(), nullable=True, comment='配送时间原始JSON信息'),
    sa.Column('delivery_time', sa.DateTime(), nullable=True, comment='配送时间'),
    sa.Column('delivery_fee', sa.Float(), nullable=False, comment='配送费'),
    sa.Column('fengniao_order_id', sa.String(length=64), nullable=True, comment='蜂鸟订单号'),
    sa.Column('fengniao_status', sa.Integer(), nullable=True, comment='蜂鸟订单状态'),
    sa.ForeignKeyConstraint(['id'], ['orders.id'], name=op.f('fk_delivery_orders_id_orders')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_delivery_orders'))
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('delivery_orders')
    # ### end Alembic commands ###
