"""add_order_delivery

Revision ID: c8a2e2c6c8f2
Revises: f6a600b2724c
Create Date: 2025-09-27 16:31:58.829996

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c8a2e2c6c8f2'
down_revision: Union[str, None] = 'f6a600b2724c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('orders', 'type',
                    existing_type=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', name='ordertype'),
                    type_=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', 'DELIVERY', name='ordertype'),
                    existing_nullable=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('orders', 'type',
                    existing_type=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', 'DELIVERY', name='ordertype'),
                    type_=sa.Enum('ORDER', 'DIRECT_SALE', 'RESERVATION', 'RECHARGE', name='ordertype'),
                    existing_nullable=True)
