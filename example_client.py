#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
蜂鸟即配API调用示例脚本

本脚本展示如何直接调用蜂鸟即配服务的各种接口，包括：
1. 预下单接口
2. 创建订单接口
3. 查询订单状态接口
4. 取消订单接口

使用前请确保：
1. 已正确配置环境变量
2. 项目依赖已安装
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# 导入蜂鸟API客户端
from app.service.fengniao import FengniaoClient


def create_sample_order_data() -> Dict[str, Any]:
    """创建示例订单数据 - 符合预下单接口要求"""
    # 获取当前时间戳（毫秒）
    current_timestamp = int(time.time() * 1000)
    # 期望取货时间（30分钟后）
    expect_fetch_time = current_timestamp + (30 * 60 * 1000)
    
    return {
        # 必填参数
        "partner_order_code": f"PRE_ORDER_{current_timestamp}",  # 外部订单号（用于幂等）
        "chain_store_id": 461841708,  # 门店ID
        "order_type": 1,  # 订单类型：1-即时单，3-预约单
        "position_source": 3,  # 经纬度来源：1-腾讯地图，2-百度地图，3-高德地图
        "goods_count": 2,  # 货物件数
        "goods_total_amount_cent": 4700,  # 订单商品总金额（分）
        "goods_actual_amount_cent": 4500,  # 订单货物实付金额（分）
        "goods_weight": 1.5,  # 货物总重量（kg）
        
        # 收件人信息（必填）
        "receiver_address": "北京市海淀区上地街道彩虹大厦(开拓路)",
        "receiver_longitude": 116.307892,  # 收件人经度
        "receiver_latitude": 40.039115,   # 收件人纬度
        
        # 商品列表（必填）
        "goods_item_list": [
            {
                "item_id": "ITEM_001",
                "item_name": "素食汉堡套餐",
                "item_quantity": 1,
                "item_amount_cent": 2800,  # 商品原价（分）
                "item_actual_amount_cent": 2700,  # 商品实际支付金额（分）
                "item_remark": "不要洋葱",
                "item_size": 2  # 商品尺寸：1-小，2-中，3-大
            },
            {
                "item_id": "ITEM_002",
                "item_name": "素食披萨",
                "item_quantity": 1,
                "item_amount_cent": 1900,
                "item_actual_amount_cent": 1800,
                "item_remark": "多加芝士",
                "item_size": 3
            }
        ],
        
        # 可选参数
        "use_coupon": 1,  # 是否使用优惠券：0-不使用，1-使用（默认使用）
        "expect_fetch_time": expect_fetch_time,  # 期望取货时间
        "order_add_time": current_timestamp,  # 下单时间
        "order_tip_amount_cent": 200,  # 订单小费金额（分）
        "order_source": "0",  # 商户订单来源：0-手发单/未知来源
        
        # 以下参数仅在特定场景使用，这里注释掉
        # "transport_longitude": None,  # 取货点经度（点对点特殊服务场景）
        # "transport_latitude": None,   # 取货点纬度（点对点特殊服务场景）
        # "transport_address": None,    # 取货点地址
        # "transport_tel": None,        # 取货点联系人电话
        # "out_shop_code": None,        # 外部门店ID
        # "service_goods_id": None,     # 服务商品ID
        # "base_goods_id": None,        # 基础商品ID
        # "require_receive_time": None, # 需要送达时间（预约单必填）
        # "order_source_order_id": None, # 商户订单来源单号
        # "appoint_extra_goods_ids": [] # 指定增值商品ID列表
    }


def demo_pre_create_order():
    """专门演示预下单接口的函数"""
    print("蜂鸟即配预下单接口演示")
    print("=" * 50)
    
    # 初始化客户端
    client = FengniaoClient()
    
    # 创建示例订单数据
    order_data = create_sample_order_data()
    
    try:
        # 调用预下单接口
        print("\n🚀 开始调用预下单接口...")
        pre_order_result = client.pre_create_order(order_data)
        
        # 检查调用结果
        if pre_order_result.get("code") == "200":
            print("\n✅ 预下单接口调用成功！")
            
            # 解析业务数据，展示可选运力
            business_data = pre_order_result.get("business_data")
            if business_data and isinstance(business_data, str):
                business_data = json.loads(business_data)
                goods_infos = business_data.get("goods_infos", [])
                
                available_count = sum(1 for info in goods_infos if info.get("is_valid") == 1)
                print(f"\n📋 运力查询结果: 共找到 {len(goods_infos)} 个运力选项，其中 {available_count} 个可用")
                
                if available_count > 0:
                    print("\n💡 建议: 可以选择其中一个可用的运力选项进行正式下单")
                else:
                    print("\n⚠️  注意: 当前没有可用的运力，可能需要调整订单参数或稍后重试")
        else:
            print(f"\n❌ 预下单接口调用失败")
            print(f"错误码: {pre_order_result.get('code')}")
            print(f"错误信息: {pre_order_result.get('msg', pre_order_result.get('message'))}")
            
    except Exception as e:
        print(f"\n💥 预下单演示过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()


def demo_all_interfaces():
    """演示所有接口的综合示例"""
    print("蜂鸟即配全接口演示")
    print("=" * 60)
    
    # 初始化客户端
    client = FengniaoClient()
    
    # 创建示例订单数据
    order_data = create_sample_order_data()
    partner_order_code = order_data["partner_order_code"]
    
    try:
        # === 第一阶段：预下单和创建订单 ===
        print("\n🚀 第一阶段: 预下单和创建订单")
        print("-" * 40)
        
        # 1. 预下单
        print("\n步骤1: 预下单")
        pre_order_result = client.pre_create_order(order_data)
        
        if pre_order_result.get("code") != "200":
            print(f"预下单失败，停止演示: {pre_order_result.get('message', pre_order_result.get('msg'))}")
            return
        
        # 从预下单结果中获取可用的运力信息
        business_data = pre_order_result.get("business_data")
        selected_goods_info = None
        
        if business_data and isinstance(business_data, str):
            business_data = json.loads(business_data)
            goods_infos = business_data.get("goods_infos", [])
            
            # 选择第一个可用的商品进行后续下单
            for goods_info in goods_infos:
                if goods_info.get("is_valid") == 1:
                    selected_goods_info = goods_info
                    print(f"\n✅ 选择可用运力商品进行下单")
                    # 为正式下单准备参数
                    if goods_info.get("t_index_id"):
                        order_data["pre_create_order_t_index_id"] = goods_info.get("t_index_id")
                    if goods_info.get("service_goods_id"):
                        order_data["service_goods_id"] = goods_info.get("service_goods_id")
                    if goods_info.get("base_goods_id"):
                        order_data["base_goods_id"] = goods_info.get("base_goods_id")
                    if goods_info.get("actual_delivery_amount_cent"):
                        order_data["actual_delivery_amount_cent"] = goods_info.get("actual_delivery_amount_cent")
                    break
            
            if not selected_goods_info:
                print("❌ 没有找到可用的运力商品，无法继续下单演示")
                return
        
        # 2. 正式创建订单
        print("\n步骤2: 正式创建订单")
        # 添加创建订单必须的额外参数
        order_data.update({
            "receiver_name": "张三",
            "receiver_primary_phone": "***********",
            "serial_number": f"SN{int(time.time())}"  # 商家订单流水号
        })
        
        create_result = client.create_order(order_data)
        
        if create_result.get("code") != "200":
            print(f"创建订单失败: {create_result.get('message', create_result.get('msg'))}")
            return
        
        # 获取蜂鸟订单号
        fengniao_order_no = None
        business_data = create_result.get("business_data")
        if business_data and isinstance(business_data, str):
            business_data = json.loads(business_data)
            fengniao_order_no = business_data.get("order_id")
        
        if not fengniao_order_no:
            print("❌ 未能获取到蜂鸟订单号，无法继续后续演示")
            return
        
        print(f"✅ 订单创建成功！蜂鸟订单号: {fengniao_order_no}")
        
        # # === 第二阶段：订单查询和管理 ===
        # print("\n🔍 第二阶段: 订单查询和管理")
        # print("-" * 40)
        #
        # # 3. 查询订单详情
        # print("\n步骤3: 查询订单详情")
        # order_query = {
        #     "partner_order_code": partner_order_code,
        #     "order_id": fengniao_order_no
        # }
        # detail_result = client.get_order_detail(order_query)
        #
        # # 4. 查询骑手信息
        # print("\n步骤4: 查询骑手信息")
        # knight_result = client.get_knight_info(order_query)
        #
        # # 5. 获取取消原因列表
        # print("\n步骤5: 获取取消原因列表")
        # cancel_reasons = client.get_cancel_reason_list({"order_id": fengniao_order_no})
        #
        # # === 第三阶段：订单操作演示 ===
        # print("\n⚙️  第三阶段: 订单操作演示")
        # print("-" * 40)
        #
        # # 6. 加小费演示（如果订单支持）
        # print("\n步骤6: 加小费演示")
        # tip_data = {
        #     "order_id": fengniao_order_no,
        #     "partner_order_code": partner_order_code,
        #     "third_index_id": int(time.time()),  # 本次加小费唯一标识
        #     "add_tip_amount_cent": 100  # 加小费1元
        # }
        # tip_result = client.add_tip(tip_data)
        #
        # # 7. 预取消订单演示
        # print("\n步骤7: 预取消订单演示")
        # cancel_data = {
        #     "order_id": fengniao_order_no,
        #     "partner_order_code": partner_order_code,
        #     "order_cancel_code": 32  # 订单信息填写错误
        # }
        # pre_cancel_result = client.pre_cancel_order(cancel_data)
        #
        # # 8. 正式取消订单演示（可选，会真正取消订单）
        # print("\n步骤8: 正式取消订单演示")
        # print("⚠️  注意: 此步骤会真正取消订单，演示环境可以执行")
        #
        # # 从预取消结果中获取取消费用
        # cancel_cost = 0
        # if pre_cancel_result.get("code") == "200":
        #     pre_cancel_business_data = pre_cancel_result.get("business_data")
        #     if pre_cancel_business_data and isinstance(pre_cancel_business_data, str):
        #         pre_cancel_business_data = json.loads(pre_cancel_business_data)
        #         cancel_cost = pre_cancel_business_data.get("actual_cancel_cost_cent", 0)
        #
        # final_cancel_data = {
        #     "order_id": fengniao_order_no,
        #     "partner_order_code": partner_order_code,
        #     "order_cancel_code": 32,
        #     "order_cancel_role": 1,  # 1商户取消
        #     "order_cancel_other_reason": "演示测试取消",
        #     "actual_cancel_cost_cent": cancel_cost
        # }
        # cancel_result = client.cancel_order(final_cancel_data)
        #
        # # === 第四阶段：投诉和索赔演示 ===
        # print("\n📋 第四阶段: 投诉和索赔演示")
        # print("-" * 40)
        #
        # # 9. 订单投诉演示
        # print("\n步骤9: 订单投诉演示")
        # complaint_data = {
        #     "order_id": fengniao_order_no,
        #     "partner_order_code": partner_order_code,
        #     "order_complaint_code": 160,  # 投诉骑手服务态度差
        #     "order_complaint_desc": "演示投诉：服务态度需要改善"
        # }
        # complaint_result = client.complaint_order(complaint_data)
        #
        # # 10. 订单索赔演示
        # print("\n步骤10: 订单索赔演示")
        # claim_data = {
        #     "order_id": fengniao_order_no,
        #     "partner_order_code": partner_order_code,
        #     "order_claim_code": 300,  # 配送超时
        #     "order_claim_price_cent": 500,  # 索赔5元
        #     "order_claim_desc": "演示索赔：配送超时造成损失",
        #     "order_claim_sku": [
        #         {
        #             "claim_sku_name": "素食汉堡套餐",
        #             "claim_sku_goods_count": "1",
        #             "claim_sku_price_cent": "2700",
        #             "claim_sku_total_price_cent": "2700"
        #         }
        #     ]
        # }
        # claim_result = client.claim_order(claim_data)
        #
        # print("\n🎉 全接口演示完成!")
        # print("=" * 60)
        # print("💡 说明:")
        # print("- 以上演示涵盖了蜂鸟即配的主要订单管理接口")
        # print("- 包括：预下单、创建订单、查询详情、骑手信息、取消流程、加小费、投诉、索赔等")
        # print("- 实际使用时请根据业务需求调整参数")
        # print("- 某些接口可能因为订单状态或权限限制而失败，这是正常现象")
        # print("- 投诉和索赔功能请谨慎使用，仅在必要时调用")
        
    except Exception as e:
        print(f"\n💥 演示过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()


def show_api_documentation():
    """显示所有可用接口的文档说明"""
    print("\n📚 蜂鸟即配API接口文档")
    print("=" * 60)
    
    interfaces = [
        ("01", "preCreateOrder", "预下单接口", "返回可选运力列表和价格信息，用于价格校验"),
        ("02", "createOrder", "正式下单接口", "创建正式订单，返回蜂鸟订单号"),
        ("03", "addTip", "加小费接口", "为订单追加小费，累加计算"),
        ("04", "getCancelReasonList", "获取取消原因接口", "获取可用的订单取消原因列表"),
        ("05", "preCancelOrder", "预取消订单接口", "获取取消价格，用于取消费用校验"),
        ("06", "cancelOrder", "正式取消订单接口", "正式取消订单，可能产生取消费用"),
        ("07", "getOrderDetail", "查询订单详情接口", "获取订单的详细状态和信息"),
        ("08", "getKnightInfo", "查询骑手信息接口", "获取配送骑手的位置和联系信息"),
        ("09", "complaintOrder", "订单投诉接口", "对配送服务进行投诉"),
        ("10", "claimOrder", "订单索赔接口", "对配送问题申请索赔"),
        ("11", "consultOrder", "订单咨询接口", "对订单进行咨询，如催单等"),
        ("12", "getConsultDetail", "查询订单咨询详情", "查看咨询结果详情"),
        ("13", "standardSyncOrderInfo", "订单信息同步接口", "同步订单信息如实际出餐时间"),
        ("14", "standardPreModifyOrder", "预询修改订单信息", "预询修改订单信息的费用"),
        ("15", "standardModifyOrder", "正式修改订单信息", "正式修改订单收货信息等")
    ]
    
    print("接口列表:")
    for num, api_name, cn_name, description in interfaces:
        print(f"{num}. {cn_name} ({api_name})")
        print(f"   📝 {description}")
        print()
    
    print("💡 使用建议:")
    print("- 新手建议先运行'预下单接口演示'了解基础流程")
    print("- 完整业务流程建议运行'全接口综合演示'")
    print("- 生产环境使用前请仔细阅读各接口的参数要求")
    print("- 投诉和索赔接口请谨慎使用，避免误操作")
    print("=" * 60)


def main():
    """主函数 - 演示完整的API调用流程"""
    print("蜂鸟即配API调用示例")
    print("=" * 50)
    
    # 初始化客户端
    client = FengniaoClient()
    
    # 创建示例订单数据
    order_data = create_sample_order_data()
    partner_order_code = order_data["partner_order_code"]
    
    try:
        # 1. 预下单
        print("\n步骤1: 预下单")
        pre_order_result = client.pre_create_order(order_data)
        
        if pre_order_result.get("code") != "200":
            print(f"预下单失败: {pre_order_result.get('message', pre_order_result.get('msg'))}")
            return
        
        # 从预下单结果中获取可用的运力信息
        business_data = pre_order_result.get("business_data")
        selected_goods_info = None
        
        if business_data and isinstance(business_data, str):
            business_data = json.loads(business_data)
            goods_infos = business_data.get("goods_infos", [])
            
            # 选择第一个可用的商品进行后续下单
            for goods_info in goods_infos:
                if goods_info.get("is_valid") == 1:  # 1表示可用
                    selected_goods_info = goods_info
                    print(f"\n✅ 选择可用运力商品:")
                    print(f"   服务商品ID: {goods_info.get('service_goods_id')}")
                    print(f"   基础商品ID: {goods_info.get('base_goods_id')}")
                    print(f"   预询标识: {goods_info.get('t_index_id')}")
                    print(f"   配送费用: {goods_info.get('actual_delivery_amount_cent')} 分")
                    print(f"   预计送达: {goods_info.get('predict_delivery_minutes')} 分钟")
                    break
            
            if not selected_goods_info:
                print("❌ 没有找到可用的运力商品，无法继续下单")
                return
            
            # 为正式下单准备参数
            if selected_goods_info.get("t_index_id"):
                order_data["t_index_id"] = selected_goods_info.get("t_index_id")
            if selected_goods_info.get("service_goods_id"):
                order_data["service_goods_id"] = selected_goods_info.get("service_goods_id")
            if selected_goods_info.get("base_goods_id"):
                order_data["base_goods_id"] = selected_goods_info.get("base_goods_id")
        
        # 2. 正式创建订单
        print("\n步骤2: 正式创建订单")
        create_result = client.create_order(order_data)
        
        if create_result.get("code") != "200":
            print(f"创建订单失败: {create_result.get('message')}")
            return
        
        # 获取蜂鸟订单号
        fengniao_order_no = None
        business_data = create_result.get("business_data")
        if business_data and isinstance(business_data, str):
            business_data = json.loads(business_data)
            fengniao_order_no = business_data.get("order_id")
        
        print(f"✅ 订单创建成功！蜂鸟订单号: {fengniao_order_no}")
        
        # 3. 查询订单详情
        print("\n步骤3: 查询订单详情")
        order_query = {
            "partner_order_code": partner_order_code
        }
        if fengniao_order_no:
            order_query["fengniao_order_no"] = fengniao_order_no
        
        detail_result = client.get_order_detail(order_query)
        
        # 4. 获取取消原因列表（可选）
        print("\n步骤4: 获取取消原因列表")
        # 根据API文档，需要传入order_id参数
        cancel_reason_query = {}
        if fengniao_order_no:
            # 如果有蜂鸟订单号，使用order_id参数
            cancel_reason_query["order_id"] = fengniao_order_no
        else:
            # 如果没有蜂鸟订单号，可能需要其他参数或者跳过此步骤
            print("警告: 没有蜂鸟订单号，跳过获取取消原因列表")
            cancel_reasons = {"code": 400, "message": "缺少蜂鸟订单号"}
        
        if cancel_reason_query:
            cancel_reasons = client.get_cancel_reason_list(cancel_reason_query)
        
        # 5. 预取消订单示例（可选，仅作演示）
        print("\n步骤5: 预取消订单示例")
        cancel_data = {
            "order_cancel_code": 1
        }
        if fengniao_order_no:
            cancel_data["order_id"] = fengniao_order_no
        
        pre_cancel_result = client.pre_cancel_order(cancel_data)
        
        print("\n=== API调用示例完成 ===")
        print("注意：以上示例仅用于演示API调用方法，实际使用时请根据业务需求调整参数")
        
    except Exception as e:
        print(f"\n执行过程中发生异常: {str(e)}")
        import traceback
        traceback.print_exc()


def test_service_config():
    """测试服务配置"""
    try:
        # 尝试创建蜂鸟客户端来验证配置
        FengniaoClient()
        print("蜂鸟服务配置检查通过")
        return True
    except Exception as e:
        print(f"蜂鸟服务配置检查失败: {str(e)}")
        print("请确保已正确配置环境变量")
        return False


if __name__ == "__main__":
    print("检查服务配置...")
    if test_service_config():
        print("\n🎯 蜂鸟即配API演示程序")
        print("=" * 40)
        print("请选择运行模式：")
        print("1. 预下单接口演示（推荐新手）")
        print("2. 全接口综合演示（完整流程）") 
        print("3. 原始完整流程演示（兼容旧版）")
        print("4. 查看接口文档说明")
        
        try:
            choice = input("\n请输入选择 (1-4，默认为 1): ").strip()
            
            if choice == "2":
                demo_all_interfaces()
            elif choice == "3":
                main()
            elif choice == "4":
                show_api_documentation()
            else:
                demo_pre_create_order()
                
        except KeyboardInterrupt:
            print("\n\n程序已取消")
        except Exception as e:
            print(f"\n输入处理错误: {e}")
            # 默认运行预下单演示
            demo_pre_create_order()
    else:
        print("\n⚠️  配置检查失败，请检查以下配置：")
        print("1. 确保已正确设置蜂鸟即配相关的环境变量")
        print("2. 确保项目依赖已安装")
        print("3. 确保数据库和Redis连接正常")