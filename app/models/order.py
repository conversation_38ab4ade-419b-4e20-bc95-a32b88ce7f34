import enum
from datetime import datetime

from sqlalchemy import Column, Integer, Float, ForeignKey, Enum, DateTime, String, JSON, Boolean
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.utils.common import get_current_time


class OrderType(enum.Enum):
    ORDER = "order"
    DIRECT_SALE = "direct_sale"  # 直销订单：直接购买商品
    RESERVATION = "reservation"  # 预订订单：需要预订的商品订单
    RECHARGE = "recharge"  # 充值订单：充值账户余额
    DELIVERY = "delivery"  # 外卖订单：需要配送的商品订单


class OrderStatus(enum.Enum):
    """订单状态枚举
    定义了订单在整个生命周期中的状态
    """
    # 用于主状态
    PENDING = "pending"  # 待处理：订单已创建但未支付
    PAID = "paid"  # 已支付：订单已支付但未发货
    CANCELLED = "cancelled"  # 已取消：订单已取消
    COMPLETED = "completed"  # 已完成：订单已完成

    # 用于子状态
    SHIPPED = "shipped"  # 已发货：订单已发货但未送达
    DELIVERED = "delivered"  # 已送达：订单已送达客户
    VERIFIED = "verified"  # 已核销：订单已核销
    REFUNDED = "refunded"  # 已退款：订单已退款

    REFUNDED_PARTIAL = "refunded_partial"  # 部分退款：订单部分退款
    PARTIAL_PAID = "partial_paid"  # 部分支付：订单部分支付

class PaymentStatus(enum.Enum):
    """支付状态枚举
    定义了订单的支付状态
    """
    UNPAID = "unpaid"  # 未支付：订单尚未支付
    PAID = "paid"  # 已支付：订单已支付
    PARTIAL_PAID = "partial_paid"  # 部分支付：订单部分支付（用于混合支付）
    REFUNDED = "refunded"  # 已退款：订单已退款


class PaymentMethod(enum.Enum):
    """支付方式枚举
    定义了系统支持的支付方式
    """
    ACCOUNT_BALANCE = "account_balance"  # 帐户余额
    ENTERPRISE_ACCOUNT_BALANCE = "enterprise_account_balance"  # 企业帐户支付
    CASH = "cash"  # 现金支付
    ALIPAY = "alipay"  # 支付宝支付
    WECHAT_PAY = "wechat_pay"  # 微信支付
    BANK_TRANSFER = "bank_transfer"  # 银行转账


class ReservationStatus(enum.Enum):
    """预订状态枚举
    定义了预订订单的状态
    """
    PENDING = "pending"  # 待审批：预订申请已提交但未审批
    APPROVED = "approved"  # 已通过：预订申请已通过
    REJECTED = "rejected"  # 已拒绝：预订申请已拒绝


class Order(Base):
    """订单基类
    用于管理订单基本信息的基础类，支持多态继承
    属性：
        id: 订单唯一标识
        order_no: 订单编号
        user_id: 用户ID
        status: 订单状态
        payment_status: 支付状态
        payment_time: 支付时间
        total_amount: 订单总金额
        actual_amount_paid: 实际支付金额
        payment_method: 支付方式
        type: 订单类型
        create_time: 创建时间
        update_time: 更新时间
        is_modified: 是否被修改过
        modification_count: 修改次数
        last_modified_at: 最后修改时间
        original_amount: 原始订单金额
    关系：
        user: 与用户的多对一关系
        items: 与订单项的一对多关系
        transactions: 与账户流水的一对多关系
        wx_payment_records: 与微信支付记录的一对多关系
    """
    __tablename__ = "orders"

    id = Column(Integer, primary_key=True, index=True, comment="订单唯一标识")
    order_no = Column(String(50), unique=True, index=True, comment="订单编号")
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False, comment="用户ID")
    status = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING, comment="订单状态")
    payment_status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.UNPAID, comment="支付状态")
    payment_time = Column(DateTime, nullable=True, comment="支付时间")
    total_amount = Column(Float, nullable=False, comment="订单总金额")
    payable_amount = Column(Float, nullable=False, comment="应付金额")
    actual_amount_paid = Column(Float, nullable=False, comment="实际支付金额")
    discount_amount = Column(Float, nullable=False, default=0, comment="优惠金额")
    payment_method = Column(Enum(PaymentMethod), nullable=False, default=PaymentMethod.ACCOUNT_BALANCE,
                            comment="支付方式")
    pricing_remark = Column(String(200), nullable=True, default="", comment="计价说明")
    type = Column(Enum(OrderType), nullable=False, default=OrderType.ORDER, comment="订单类型")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 新增字段，用于跟踪订单修改状态
    is_modified = Column(Boolean, default=False, comment="订单是否被修改过")
    modification_count = Column(Integer, default=0, comment="订单修改次数")
    last_modified_at = Column(DateTime, nullable=True, comment="最后修改时间")
    original_amount = Column(Float, nullable=True, comment="原始订单金额")
    current_status = Column(String(200), nullable=True, comment="当前订单状态描述")

    # 新增混合支付相关字段
    enterprise_paid_amount = Column(Float, nullable=True, default=0, comment="企业已支付金额")
    personal_paid_amount = Column(Float, nullable=True, default=0, comment="个人已支付金额")
    requires_personal_payment = Column(Float, nullable=True, default=0, comment="需要个人支付的金额")
    is_split_payment = Column(Boolean, default=False, comment="是否为分次支付订单")

    __mapper_args__ = {
        'polymorphic_on': type,
        'polymorphic_identity': OrderType.ORDER
    }
    # 关联
    user = relationship("User", back_populates="orders")
    items = relationship("OrderItem", back_populates="order")
    transactions = relationship("AccountTransaction", back_populates="order")
    wx_payment_records = relationship("WxPaymentRecord", back_populates="order")
    reservation_requests = relationship("ReservationRequest", back_populates="order")
    coupon_usage_records = relationship("CouponUsageRecord", back_populates="order")


class OrderItem(Base):
    __tablename__ = "order_items"

    id = Column(Integer, primary_key=True, index=True, comment="订单项唯一标识")
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, comment="订单ID")
    product_id = Column(Integer, ForeignKey("products.id"), nullable=False, comment="商品ID")
    quantity = Column(Integer, nullable=False, comment="购买数量")
    unpaid_quantity = Column(Integer, nullable=False, default=0, comment="未支付数量")
    price = Column(Float, nullable=False, comment="商品单价")
    subtotal = Column(Float, nullable=False, comment="小计金额")
    final_price = Column(Float, nullable=False, comment="最终单价")
    payable_amount = Column(Float, nullable=False, comment="应付金额")
    discount_amount = Column(Float, nullable=False, default=0, comment="优惠金额")
    pricing_remark = Column(String(200), nullable=True, default="", comment="计价说明")
    status = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING, comment="订单状态")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    order = relationship("Order", back_populates="items")
    product = relationship("Product", back_populates="order_items")
    reservation_requests = relationship("ReservationRequest", back_populates="order_item")
    coupon_usage_records = relationship("CouponUsageRecord", back_populates="order_item")


class RechargeOrder(Order):
    """充值订单类"""
    __tablename__ = "recharge_orders"

    id = Column(Integer, ForeignKey("orders.id"), primary_key=True)
    # 如果充值订单需要额外字段，可以在这里添加

    __mapper_args__ = {
        "polymorphic_identity": OrderType.RECHARGE
    }


class ReservationOrder(Order):
    """预订订单类"""
    __tablename__ = "reservation_orders"

    id = Column(Integer, ForeignKey("orders.id"), primary_key=True)
    # 如果预订订单需要额外字段，可以在这里添加
    reservation_status = Column(Enum(ReservationStatus), nullable=False, default=ReservationStatus.PENDING, comment="预订状态")

    __mapper_args__ = {
        "polymorphic_identity": OrderType.RESERVATION
    }


class DirectSaleOrder(Order):
    """直销订单类"""
    __tablename__ = "direct_sale_orders"

    id = Column(Integer, ForeignKey("orders.id"), primary_key=True)
    # 如果直销订单需要额外字段，可以在这里添加

    __mapper_args__ = {
        "polymorphic_identity": OrderType.DIRECT_SALE
    }


class DeliveryOrder(Order):
    """外卖订单类"""
    __tablename__ = "delivery_orders"

    id = Column(Integer, ForeignKey("orders.id"), primary_key=True)

    # 外卖订单特有字段
    delivery_address_raw = Column(JSON, nullable=True, comment="配送地址原始JSON信息")
    delivery_address = Column(String(500), nullable=True, comment="配送地址字符串")
    delivery_time_raw = Column(JSON, nullable=True, comment="配送时间原始JSON信息")
    delivery_time = Column(DateTime, nullable=True, comment="配送时间")
    delivery_fee = Column(Float, nullable=False, default=0, comment="配送费")
    fengniao_order_id = Column(String(64), nullable=True, comment="蜂鸟订单号")
    fengniao_status = Column(Integer, nullable=True, comment="蜂鸟订单状态")

    __mapper_args__ = {
        "polymorphic_identity": OrderType.DELIVERY
    }


class WxPaymentStatus(enum.Enum):
    """微信支付状态枚举"""
    UNPAID = "unpaid"  # 未支付
    SUCCESS = "success"  # 支付成功
    REFUND = "refund"  # 已退款
    CLOSED = "closed"  # 已关闭
    NOTPAY = "notpay"  # 未支付
    USERPAYING = "userpaying"  # 用户支付中
    PAYERROR = "payerror"  # 支付失败


class WxPaymentRecord(Base):
    """微信支付记录表
    用于存储微信支付相关的所有信息
    """
    __tablename__ = "wx_payment_records"

    id = Column(Integer, primary_key=True, index=True, comment="支付记录ID")
    order_id = Column(Integer, ForeignKey("orders.id"), nullable=False, comment="关联订单ID")
    order_no = Column(String(50), nullable=False, index=True, comment="商户订单号")
    transaction_id = Column(String(50), nullable=True, index=True, comment="微信支付订单号")
    prepay_id = Column(String(64), nullable=True, comment="预支付交易会话标识")
    openid = Column(String(50), nullable=False, comment="用户OpenID")
    total_amount = Column(Float, nullable=False, comment="订单总金额(元)")
    payer_total = Column(Float, nullable=True, comment="用户实际支付金额(元)")
    currency = Column(String(10), nullable=False, default="CNY", comment="货币类型")
    status = Column(Enum(WxPaymentStatus), nullable=False, default=WxPaymentStatus.UNPAID, comment="支付状态")
    description = Column(String(128), nullable=False, comment="商品描述")
    trade_state = Column(String(32), nullable=True, comment="交易状态")
    trade_state_desc = Column(String(256), nullable=True, comment="交易状态描述")
    bank_type = Column(String(32), nullable=True, comment="付款银行类型")
    success_time = Column(DateTime, nullable=True, comment="支付完成时间")
    payment_request_params = Column(JSON, nullable=True, comment="支付请求参数")
    notify_data = Column(JSON, nullable=True, comment="支付回调数据")
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    order = relationship("Order", back_populates="wx_payment_records")
    refunds = relationship("WxRefundRecord", back_populates="payment")


class WxRefundStatus(enum.Enum):
    """微信退款状态枚举"""
    PROCESSING = "processing"  # 处理中
    SUCCESS = "success"  # 退款成功
    ABNORMAL = "abnormal"  # 退款异常
    CLOSED = "closed"  # 退款关闭


class WxRefundRecord(Base):
    """微信退款记录表
    用于存储微信退款相关的所有信息
    """
    __tablename__ = "wx_refund_records"

    id = Column(Integer, primary_key=True, index=True, comment="退款记录ID")
    payment_id = Column(Integer, ForeignKey("wx_payment_records.id"), nullable=False, comment="关联支付记录ID")
    refund_id = Column(String(50), nullable=True, index=True, comment="微信退款单号")
    out_refund_no = Column(String(50), nullable=False, index=True, comment="商户退款单号")
    transaction_id = Column(String(50), nullable=False, comment="微信支付订单号")
    total_amount = Column(Float, nullable=False, comment="原订单金额(元)")
    refund_amount = Column(Float, nullable=False, comment="退款金额(元)")
    currency = Column(String(10), nullable=False, default="CNY", comment="货币类型")
    status = Column(Enum(WxRefundStatus), nullable=False, default=WxRefundStatus.PROCESSING, comment="退款状态")
    reason = Column(String(128), nullable=True, comment="退款原因")
    refund_time = Column(DateTime, nullable=True, comment="退款成功时间")
    notify_data = Column(JSON, nullable=True, comment="退款回调数据")
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    payment = relationship("WxPaymentRecord", back_populates="refunds")