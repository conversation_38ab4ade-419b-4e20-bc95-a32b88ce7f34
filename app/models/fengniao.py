"""蜂鸟配送回调相关模型"""
import enum
from datetime import datetime
from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, BigInteger
from sqlalchemy.dialects.mysql import JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base


class FengniaoCallbackType(enum.Enum):
    """蜂鸟回调类型枚举"""
    ORDER_STATUS_NOTIFY = "orderStatusNotify"  # 订单状态变更回调
    REVERSE_ORDER_NOTIFY = "reverseOrderNotify"  # 逆向订单状态回调
    CHAINSTORE_STATUS_NOTIFY = "chainstoreStatusNotify"  # 门店状态变更回调
    CHAINSTORE_SERVICE_STATUS_NOTIFY = "chainstoreServiceStatusNotify"  # 门店配送范围变更回调
    CHAINSTORE_BUSINESS_TIME_NOTIFY = "chainstoreBusinessTimeNotify"  # 门店营业时间变更回调
    ABNORMAL_REPORT_NOTIFY = "abnormalReportNotify"  # 异常报备回调
    COOKING_FINISH_NOTIFY = "cookingFinishNotify"  # 商户出餐回调


class FengniaoCallback(Base):
    """蜂鸟回调记录表"""
    __tablename__ = "fengniao_callbacks"

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    app_id = Column(String(64), nullable=False, comment="应用ID")
    timestamp = Column(BigInteger, nullable=False, comment="时间戳(毫秒)")
    update_time = Column(DateTime, nullable=False, comment="可读时间格式")
    signature = Column(String(256), nullable=True, comment="签名")
    business_data = Column(JSON, nullable=False, comment="业务数据(JSON格式)")
    callback_type = Column(String(64), nullable=False, comment="回调类型")
    raw_data = Column(JSON, nullable=True, comment="原始回调数据")
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    order_status_callbacks = relationship("FengniaoCallbackOrderStatus", back_populates="callback")


class FengniaoCallbackOrderStatus(Base):
    """蜂鸟订单状态变更回调详细信息表"""
    __tablename__ = "fengniao_callbacks_order_status"

    id = Column(Integer, primary_key=True, index=True, comment="主键ID")
    callback_id = Column(Integer, ForeignKey("fengniao_callbacks.id"), nullable=False, comment="回调记录ID")
    
    # 订单相关信息
    order_id = Column(BigInteger, nullable=True, comment="蜂鸟订单号")
    app_id = Column(String(64), nullable=True, comment="应用ID")
    partner_order_code = Column(String(128), nullable=True, comment="外部订单号")
    order_status = Column(Integer, nullable=True, comment="订单状态")
    
    # 骑手相关信息
    carrier_driver_id = Column(BigInteger, nullable=True, comment="骑手ID")
    carrier_driver_name = Column(String(64), nullable=True, comment="骑手姓名")
    carrier_driver_phone = Column(String(32), nullable=True, comment="骑手电话")
    carrier_lat = Column(String(32), nullable=True, comment="骑手纬度")
    carrier_lng = Column(String(32), nullable=True, comment="骑手经度")
    
    # 状态描述信息
    description = Column(Text, nullable=True, comment="描述")
    detail_description = Column(Text, nullable=True, comment="详情描述")
    error_code = Column(String(64), nullable=True, comment="异常code")
    error_scene = Column(String(128), nullable=True, comment="异常描述")
    
    # 时间信息
    push_time = Column(BigInteger, nullable=True, comment="状态推送时间(毫秒)")
    
    # 其他信息
    transfer = Column(Integer, nullable=True, comment="转单标识(1是转单,0非转单)")
    api_code = Column(String(64), nullable=True, comment="订单状态回调错误码")
    api_msg = Column(Text, nullable=True, comment="订单状态回调错误信息描述")
    complete_pics = Column(JSON, nullable=True, comment="送达照片URL列表")
    state = Column(Integer, nullable=True, comment="逆向运单状态")
    shipping_order_id = Column(BigInteger, nullable=True, comment="正向运单ID")
    reverse_shipping_order_id = Column(BigInteger, nullable=True, comment="售中逆向运单ID")
    
    created_at = Column(DateTime, nullable=False, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.now, onupdate=datetime.now, comment="更新时间")

    # 关联
    callback = relationship("FengniaoCallback", back_populates="order_status_callbacks")
