from typing import List, Optional, Union
import logging
from sqlalchemy.orm import Session
from datetime import datetime, time, timedelta

from app.dao.base import DAO
from app.models.order import Order, OrderItem, OrderStatus, PaymentStatus, PaymentMethod, OrderType, RechargeOrder, ReservationOrder, DirectSaleOrder, DeliveryOrder
from app.models.reservation import ReservationType
from app.schemas.order import OrderCreate, OrderUpdate, OrderItemCreate, OrderItemUpdate, RechargeOrderCreate, DirectSaleOrderCreate, ReservationOrderCreate, DeliveryOrderCreate, RechargeOrderUpdate, DirectSaleOrderUpdate, ReservationOrderUpdate, DeliveryOrderUpdate
from app.models.account import AccountTransaction, TransactionType
from app.models.reservation import ReservationRequest, ReservationStatus
from app.utils.common import create_order_no

# 获取logger
logger = logging.getLogger(__name__)


class OrderDAO(DAO):
    """订单DAO类 - 支持多态查询
    
    该DAO类支持SQLAlchemy的多态继承，能够处理以下订单类型：
    - Order: 基础订单类型
    - RechargeOrder: 充值订单
    - ReservationOrder: 预订订单
    - DirectSaleOrder: 直销订单
    
    多态查询特性：
    1. get() 方法会自动返回正确的订单子类实例
    2. 查询方法会根据订单类型自动实例化对应的子类
    3. 支持按订单类型过滤查询
    4. 所有返回的订单对象都是其真实的子类实例
    
    使用示例：
        order = order_dao.get(session, order_id)
        if isinstance(order, RechargeOrder):
            # 处理充值订单特有逻辑
            pass
        elif isinstance(order, ReservationOrder):
            # 处理预订订单特有逻辑
            pass
    """

    def __init__(self):
        super().__init__(Order)

    def _get_order_model_class(self, order_type: OrderType) -> type:
        """根据订单类型获取对应的模型类"""
        order_model_mapping = {
            OrderType.ORDER: Order,
            OrderType.DIRECT_SALE: DirectSaleOrder,
            OrderType.RESERVATION: ReservationOrder,
            OrderType.RECHARGE: RechargeOrder,
            OrderType.DELIVERY: DeliveryOrder,
        }
        return order_model_mapping.get(order_type, Order)

    def create(self, session: Session, order: Union[OrderCreate, RechargeOrderCreate, DirectSaleOrderCreate, ReservationOrderCreate, DeliveryOrderCreate]) -> Order:
        """创建订单 - 支持多态订单创建"""
        order_data = order.model_dump()
        
        # 确保订单号存在
        if "order_no" not in order_data or not order_data["order_no"]:
            order_data["order_no"] = create_order_no()
        
        # 获取订单类型并确定使用的模型类
        order_type = order_data.get("type", OrderType.ORDER)
        if isinstance(order_type, str):
            order_type = OrderType(order_type)
        
        OrderModelClass = self._get_order_model_class(order_type)
        
        # 如果包含订单项，需要先移除
        if "items" in order_data:
            items_data = order_data.pop("items")
            
            # 使用对应的模型类创建订单
            order_obj = OrderModelClass(**order_data)
            session.add(order_obj)
            session.flush()  # 获取订单ID但不提交事务
            
            # 创建订单项
            for item_data in items_data:
                item_data["order_id"] = order_obj.id
                order_item_dao.create(session, OrderItemCreate(**item_data))
            
            session.commit()
            # 重新获取完整的订单对象（包含关联数据）
            order_obj = self.get(session, order_obj.id)
            return order_obj
        
        # 没有订单项的情况，直接创建订单
        order_obj = OrderModelClass(**order_data)
        session.add(order_obj)
        session.commit()
        session.refresh(order_obj)
        return order_obj

    def create_order_by_type(self, session: Session, order_type: OrderType, items: Optional[List[dict]] = None, **kwargs) -> Order:
        """根据订单类型创建相应的订单对象
        
        Args:
            session: 数据库会话
            order_type: 订单类型
            items: 订单项列表，每个订单项应包含product_id, quantity, price等字段
            **kwargs: 其他订单数据
            
        Returns:
            Order: 创建的订单对象
            
        Example:
            # 创建带订单项的充值订单
            order = order_dao.create_order_by_type(
                session=session,
                order_type=OrderType.RECHARGE,
                user_id=1,
                total_amount=500.0,
                payable_amount=500.0,
                actual_amount_paid=0.0,
                items=[
                    {
                        "product_id": 1,
                        "quantity": 2,
                        "price": 100.0,
                        "subtotal": 200.0,
                        "final_price": 100.0,
                        "payable_amount": 200.0
                    }
                ]
            )
        """
        # 根据订单类型选择对应的Schema
        schema_mapping = {
            OrderType.RECHARGE: RechargeOrderCreate,
            OrderType.DIRECT_SALE: DirectSaleOrderCreate,
            OrderType.RESERVATION: ReservationOrderCreate,
            OrderType.DELIVERY: DeliveryOrderCreate,
            OrderType.ORDER: OrderCreate,
        }
        
        SchemaClass = schema_mapping.get(order_type, OrderCreate)
        
        # 确保订单类型正确设置
        kwargs["type"] = order_type
        
        # 如果提供了订单项，将其添加到kwargs中
        if items is not None:
            # 将dict转换为OrderItemBase对象
            from app.schemas.order import OrderItemBase
            order_items = []
            for item in items:
                order_items.append(OrderItemBase(**item))
            kwargs["items"] = order_items
        
        # 创建Schema实例
        order_schema = SchemaClass(**kwargs)
        
        # 调用create方法
        return self.create(session, order_schema)

    def create_order_with_items(self, session: Session, order_type: OrderType, user_id: int, 
                               total_amount: float, payable_amount: float, actual_amount_paid: float,
                               items: List[dict], **additional_kwargs) -> Order:
        """创建订单并同时创建订单项的便利方法
        
        Args:
            session: 数据库会话
            order_type: 订单类型
            user_id: 用户ID
            total_amount: 订单总金额
            payable_amount: 应付金额
            actual_amount_paid: 实际支付金额
            items: 订单项列表
            **additional_kwargs: 其他额外参数（如payment_method等）
            
        Returns:
            Order: 创建的订单对象
            
        Example:
            order = order_dao.create_order_with_items(
                session=session,
                order_type=OrderType.DIRECT_SALE,
                user_id=1,
                total_amount=300.0,
                payable_amount=300.0,
                actual_amount_paid=0.0,
                items=[
                    {
                        "product_id": 1,
                        "quantity": 2,
                        "price": 100.0,
                        "subtotal": 200.0,
                        "final_price": 100.0,
                        "payable_amount": 200.0
                    },
                    {
                        "product_id": 2,
                        "quantity": 1,
                        "price": 100.0,
                        "subtotal": 100.0,
                        "final_price": 100.0,
                        "payable_amount": 100.0
                    }
                ],
                payment_method=PaymentMethod.WECHAT_PAY
            )
        """
        return self.create_order_by_type(
            session=session,
            order_type=order_type,
            user_id=user_id,
            total_amount=total_amount,
            payable_amount=payable_amount,
            actual_amount_paid=actual_amount_paid,
            items=items,
            **additional_kwargs
        )

    def get(self, session: Session, order_id: int) -> Optional[Order]:
        """获取订单 - 支持多态查询
        
        该方法支持多态查询，会根据订单类型自动返回对应的订单子类实例：
        - OrderType.RECHARGE -> RechargeOrder
        - OrderType.RESERVATION -> ReservationOrder  
        - OrderType.DIRECT_SALE -> DirectSaleOrder
        - OrderType.ORDER -> Order
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            
        Returns:
            Optional[Order]: 订单实例（可能是子类），如果不存在返回None
        """
        # SQLAlchemy的多态查询会自动返回正确的子类实例
        # 查询基类表会包含所有子类的数据
        return session.query(Order).filter(Order.id == order_id).first()

    def get_by_type(self, session: Session, order_type: OrderType, skip: int = 0, limit: int = 100) -> List[Order]:
        """根据订单类型获取订单列表 - 支持多态查询
        
        Args:
            session: 数据库会话
            order_type: 订单类型
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[Order]: 指定类型的订单列表（会返回对应的子类实例）
        """
        return session.query(Order).filter(Order.type == order_type).offset(skip).limit(limit).all()

    def get_recharge_orders(self, session: Session, skip: int = 0, limit: int = 100) -> List[RechargeOrder]:
        """获取充值订单列表
        
        Args:
            session: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[RechargeOrder]: 充值订单列表
        """
        return session.query(RechargeOrder).offset(skip).limit(limit).all()

    def get_reservation_orders(self, session: Session, skip: int = 0, limit: int = 100) -> List[ReservationOrder]:
        """获取预订订单列表
        
        Args:
            session: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[ReservationOrder]: 预订订单列表
        """
        return session.query(ReservationOrder).offset(skip).limit(limit).all()

    def get_direct_sale_orders(self, session: Session, skip: int = 0, limit: int = 100) -> List[DirectSaleOrder]:
        """获取直销订单列表
        
        Args:
            session: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[DirectSaleOrder]: 直销订单列表
        """
        return session.query(DirectSaleOrder).offset(skip).limit(limit).all()

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[Order]:
        """获取订单列表 - 支持多态查询
        
        该方法会返回所有类型的订单，每个订单会是其对应的子类实例。
        
        Args:
            session: 数据库会话
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[Order]: 订单列表（包含各种子类实例）
        """
        return session.query(Order).offset(skip).limit(limit).all()

    def update(self, session: Session, order_id: int, order: Union[OrderUpdate, RechargeOrderUpdate, DirectSaleOrderUpdate, ReservationOrderUpdate]) -> Optional[Order]:
        """更新订单 - 支持多态更新
        
        该方法支持多态订单更新，能够处理以下更新Schema：
        - OrderUpdate: 基础订单更新
        - RechargeOrderUpdate: 充值订单更新
        - DirectSaleOrderUpdate: 直销订单更新
        - ReservationOrderUpdate: 预订订单更新
        
        方法会自动检测当前订单的类型，并验证提供的更新Schema是否匹配。
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            order: 订单更新Schema（支持多态）
            
        Returns:
            Optional[Order]: 更新后的订单对象（正确的子类实例），如果不存在返回None
            
        Example:
            # 更新充值订单
            recharge_update = RechargeOrderUpdate(status=OrderStatus.PAID)
            updated_order = order_dao.update(session, order_id, recharge_update)
            
            # 更新预订订单
            reservation_update = ReservationOrderUpdate(
                status=OrderStatus.PAID,
                reservation_status=ReservationStatus.APPROVED
            )
            updated_order = order_dao.update(session, order_id, reservation_update)
        """
        # 首先获取现有订单以确定其类型
        existing_order = self.get(session, order_id)
        if not existing_order:
            logger.warning(f"订单不存在: {order_id}")
            return None
        
        # 验证更新Schema与订单类型的匹配性
        order_type = existing_order.type
        schema_type = type(order).__name__
        
        # 定义Schema和订单类型的映射关系
        schema_type_mapping = {
            "OrderUpdate": [OrderType.ORDER],
            "RechargeOrderUpdate": [OrderType.RECHARGE],
            "DirectSaleOrderUpdate": [OrderType.DIRECT_SALE], 
            "ReservationOrderUpdate": [OrderType.RESERVATION]
        }
        
        # 检查Schema类型是否与订单类型匹配
        allowed_types = schema_type_mapping.get(schema_type, [])
        if allowed_types and order_type not in allowed_types:
            logger.warning(f"Schema类型 {schema_type} 不匹配订单类型 {order_type.value}")
            # 对于不匹配的情况，我们仍然允许使用基础OrderUpdate进行更新
            if schema_type != "OrderUpdate":
                logger.warning(f"将使用基础OrderUpdate进行更新")
        
        # 提取更新数据
        order_data = order.model_dump(exclude_unset=True)
        
        # 记录更新操作
        logger.info(f"更新订单 {order_id} (类型: {order_type.value})，使用Schema: {schema_type}")
        logger.debug(f"更新数据: {order_data}")
        
        # 手动执行更新操作以确保多态支持
        # 不能直接调用super().update()，因为它使用的是基础DAO的get方法，可能不返回正确的多态实例
        try:
            # 直接更新现有订单实例的属性
            for key, value in order_data.items():
                if hasattr(existing_order, key):
                    setattr(existing_order, key, value)
                else:
                    logger.warning(f"订单对象没有属性: {key}")
            
            # 提交更新
            session.commit()
            logger.info(f"订单 {order_id} 更新成功")
            
            # 重新获取订单以确保返回正确的多态实例（使用OrderDAO的多态get方法）
            updated_order = self.get(session, order_id)
            return updated_order
            
        except Exception as e:
            logger.error(f"订单 {order_id} 更新失败: {str(e)}")
            session.rollback()
            return None

    def delete(self, session: Session, order_id: int) -> bool:
        """删除订单 - 支持多态删除
        
        该方法支持多态订单删除，能够正确处理以下订单类型的删除：
        - Order: 基础订单
        - RechargeOrder: 充值订单
        - ReservationOrder: 预订订单  
        - DirectSaleOrder: 直销订单
        
        方法会：
        1. 首先获取订单实例以确定其类型
        2. 根据订单类型执行相应的删除前验证和清理
        3. 删除关联的订单项
        4. 删除订单本身（SQLAlchemy会自动处理多态删除）
        
        Args:
            session: 数据库会话
            order_id: 订单ID
            
        Returns:
            bool: 删除成功返回True，失败返回False
            
        Example:
            success = order_dao.delete(session, order_id)
            if success:
                print("订单删除成功")
        """
        try:
            # 首先获取订单以确定其类型和验证存在性
            existing_order = self.get(session, order_id)
            if not existing_order:
                logger.warning(f"尝试删除不存在的订单: {order_id}")
                return False
            
            order_type = existing_order.type
            logger.info(f"开始删除订单 {order_id} (类型: {order_type.value})")
            
            # 根据订单类型执行特定的删除前检查和清理
            if not self._pre_delete_validation(session, existing_order):
                logger.warning(f"订单 {order_id} 删除前验证失败")
                return False
            
            # 删除关联的订单项
            order_items = session.query(OrderItem).filter(OrderItem.order_id == order_id).all()
            for item in order_items:
                logger.debug(f"删除订单项: {item.id}")
                session.delete(item)
            
            # 执行订单类型特定的清理操作
            self._perform_type_specific_cleanup(session, existing_order)
            
            # 删除订单本身（SQLAlchemy会自动处理多态删除）
            session.delete(existing_order)
            session.commit()
            
            logger.info(f"订单 {order_id} (类型: {order_type.value}) 删除成功")
            return True
            
        except Exception as e:
            logger.error(f"删除订单 {order_id} 失败: {str(e)}")
            logger.exception("详细异常信息")
            session.rollback()
            return False
    
    def _pre_delete_validation(self, session: Session, order: Order) -> bool:
        """删除前验证
        
        Args:
            session: 数据库会话
            order: 订单实例（多态）
            
        Returns:
            bool: 验证通过返回True，否则返回False
        """
        # 基础验证：检查订单状态是否允许删除
        non_deletable_statuses = [
            OrderStatus.PAID,
            OrderStatus.SHIPPED, 
            OrderStatus.DELIVERED,
            OrderStatus.COMPLETED
        ]
        
        if order.status in non_deletable_statuses:
            logger.warning(f"订单 {order.id} 状态为 {order.status.value}，不允许删除")
            return False
        
        # 根据订单类型进行特定验证
        if isinstance(order, RechargeOrder):
            return self._validate_recharge_order_deletion(session, order)
        elif isinstance(order, ReservationOrder):
            return self._validate_reservation_order_deletion(session, order)
        elif isinstance(order, DirectSaleOrder):
            return self._validate_direct_sale_order_deletion(session, order)
        
        return True
    
    def _validate_recharge_order_deletion(self, session: Session, order: RechargeOrder) -> bool:
        """验证充值订单是否可以删除"""
        # 充值订单特定验证逻辑
        if order.payment_status == PaymentStatus.PAID:
            logger.warning(f"充值订单 {order.id} 已支付，不允许删除")
            return False
        return True
    
    def _validate_reservation_order_deletion(self, session: Session, order: ReservationOrder) -> bool:
        """验证预订订单是否可以删除"""
        # 检查是否有关联的预订请求
        from app.models.reservation import ReservationRequest
        reservation_requests = session.query(ReservationRequest).join(
            OrderItem, ReservationRequest.order_item_id == OrderItem.id
        ).filter(OrderItem.order_id == order.id).all()
        
        for req in reservation_requests:
            if req.status != ReservationStatus.PENDING:
                logger.warning(f"预订订单 {order.id} 存在非待审批状态的预订请求，不允许删除")
                return False
        
        return True
    
    def _validate_direct_sale_order_deletion(self, session: Session, order: DirectSaleOrder) -> bool:
        """验证直销订单是否可以删除"""
        # 直销订单特定验证逻辑
        return True
    
    def _perform_type_specific_cleanup(self, session: Session, order: Order) -> None:
        """执行订单类型特定的清理操作
        
        Args:
            session: 数据库会话
            order: 订单实例（多态）
        """
        if isinstance(order, RechargeOrder):
            self._cleanup_recharge_order(session, order)
        elif isinstance(order, ReservationOrder):
            self._cleanup_reservation_order(session, order)
        elif isinstance(order, DirectSaleOrder):
            self._cleanup_direct_sale_order(session, order)
    
    def _cleanup_recharge_order(self, session: Session, order: RechargeOrder) -> None:
        """清理充值订单相关数据"""
        # 如果有相关的账户流水记录，需要处理
        logger.debug(f"清理充值订单 {order.id} 相关数据")
        # 这里可以添加充值订单特定的清理逻辑
    
    def _cleanup_reservation_order(self, session: Session, order: ReservationOrder) -> None:
        """清理预订订单相关数据"""
        # 删除关联的预订请求
        from app.models.reservation import ReservationRequest
        reservation_requests = session.query(ReservationRequest).join(
            OrderItem, ReservationRequest.order_item_id == OrderItem.id
        ).filter(OrderItem.order_id == order.id).all()
        
        for req in reservation_requests:
            logger.debug(f"删除预订请求: {req.id}")
            session.delete(req)
    
    def _cleanup_direct_sale_order(self, session: Session, order: DirectSaleOrder) -> None:
        """清理直销订单相关数据"""
        logger.debug(f"清理直销订单 {order.id} 相关数据")
        # 这里可以添加直销订单特定的清理逻辑

    def get_by_user(self, session: Session, user_id: int, skip: int = 0, limit: int = 100) -> List[Order]:
        """获取用户的订单 - 支持多态查询，按创建时间倒序，只显示最近一个月的订单
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[Order]: 用户的订单列表（包含各种子类实例），按创建时间倒序排列
        """
        from datetime import datetime, timedelta
        # 计算一个月前的日期
        one_month_ago = datetime.now() - timedelta(days=30)
        
        return (session.query(Order)
                .filter(Order.user_id == user_id)
                .filter(Order.created_at >= one_month_ago)
                .order_by(Order.created_at.desc())
                .offset(skip)
                .limit(limit)
                .all())
    
    def count_by_user(self, session: Session, user_id: int) -> int:
        """获取用户订单总数（最近一个月）
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            
        Returns:
            int: 用户最近一个月的订单总数
        """
        from datetime import datetime, timedelta
        # 计算一个月前的日期
        one_month_ago = datetime.now() - timedelta(days=30)
        
        return (session.query(Order)
                .filter(Order.user_id == user_id)
                .filter(Order.created_at >= one_month_ago)
                .count())

    def get_by_user_all(self, session: Session, user_id: int) -> List[Order]:
        """获取用户的所有订单 - 支持多态查询
        
        Args:
            session: 数据库会话
            user_id: 用户ID
            
        Returns:
            List[Order]: 用户的所有订单（包含各种子类实例）
        """
        return session.query(Order).filter(Order.user_id == user_id).all()

    def update_status(self, session: Session, order_id: int, status: OrderStatus) -> Optional[Order]:
        """更新订单状态"""
        order = self.get(session, order_id)
        if order:
            order.status = status
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def update_payment_status(self, session: Session, order_id: int, payment_status: PaymentStatus, status: OrderStatus) -> Optional[Order]:
        """更新支付状态"""
        order = self.get(session, order_id)
        if order:
            order.payment_status = payment_status
            order.payment_time = datetime.now()
            order.status = status
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def update_order_type(self, session: Session, order_id: int, order_type: OrderType) -> Optional[Order]:
        """更新订单类型"""
        order = self.get(session, order_id)
        if order:
            order.type = order_type
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def update_payment_method(self, session: Session, order_id: int, payment_method: PaymentMethod) -> Optional[Order]:
        """更新支付方式"""
        order = self.get(session, order_id)
        if order:
            order.payment_method = payment_method
            session.commit()
            # session.refresh(order)
            order = self.get(session, order_id)
            return order
        return None

    def get_by_order_no(self, session: Session, order_no: str) -> Optional[Order]:
        """根据订单号获取订单 - 支持多态查询

        Args:
            session: 数据库会话
            order_no: 订单号

        Returns:
            Optional[Order]: 订单对象（可能是子类实例），如果不存在返回 None
        """
        try:
            logger.info(f"尝试查询订单号: {order_no}")
            result = session.query(Order).filter(Order.order_no == order_no).first()
            if result:
                logger.info(f"查询成功，订单ID: {result.id}，订单类型: {result.type}")
            else:
                logger.warning(f"未找到订单号为 {order_no} 的订单")
            return result
        except Exception as e:
            logger.error(f"根据订单号获取订单失败: {str(e)}")
            logger.exception("详细异常信息")
            return None

    def cancel_update_status(self, session: Session, order_id: int, status: OrderStatus) -> Optional[Order]:
        """取消订单,更新订单状态"""
        order = self.get(session, order_id)
        if order:
            order.status = status
            session.commit()
            order = self.get(session, order_id)
            return order
        return None

    def cancel_order(self, session: Session, order_id: int) -> Optional[Order]:
        """取消订单"""
        order = self.get(session, order_id)
        if order:
            order.status = OrderStatus.REFUNDED
            order.payment_status = PaymentStatus.REFUNDED
            session.commit()
            order = self.get(session, order_id)
            return order
        return None

    def check_enterprise_order_in_day(self, session: Session, user_id: int, request_date_list: List[str]) -> bool:
        """
        检查用户在前后七天内是否已有同一企业支付成功的订单

        Args:
            session: 数据库会话
            user_id: 用户ID
            request_date_list: 订单消费日期列表
        Returns:
            bool: 如果七天内已有该企业支付成功的订单则返回True，否则返回False
        """
        try:
            # 获取当前日期
            today = datetime.now().date()
            # 计算前7天的日期
            seven_days_before = today - timedelta(days=7)
            seven_days_before_start = datetime.combine(seven_days_before, time.min)

            reservation_requests = session.query(ReservationRequest).filter(
                ReservationRequest.user_id == user_id,
                ReservationRequest.status == ReservationStatus.PAID_FULL,
                ReservationRequest.created_at >= seven_days_before_start
            ).all()

            logger.info(f"用户{user_id}前七天内预订订单数量: {len(reservation_requests)}")

            for reservation_request in reservation_requests:
                reservation_period = reservation_request.reservation_period
                reservation_period_start = reservation_period.split("_")[0]
                reservation_period_start = '20'+reservation_period_start[0:6]
                reservation_period_start = datetime.strptime(reservation_period_start, '%Y%m%d')
                logger.info(f"预订订单{reservation_request.id}的预订时间: {reservation_period_start}")
                # 01 需求调整：商务餐不占用员工自助餐名额
                if reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    logger.info(f"预订订单{reservation_request.id}是商务餐预订，跳过")
                    continue

                if reservation_period_start.date() in request_date_list:
                    logger.info(f"预订订单{reservation_period_start.date()}的预订时间在{request_date_list}中")
                    # 查询该预订订单的支付类型，区分如果非企业账户支付就跳过
                    # 通过订单项查找对应的订单
                    order_item = reservation_request.order_item
                    if order_item and order_item.order:
                        order_object = order_item.order
                        if order_object and order_object.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                            logger.info(f"订单{order_object.order_no}是企业账户支付")
                            return True, reservation_period_start
                        else:
                            logger.info(f"订单{order_object.order_no if order_object else '未知'}不是企业账户支付，跳过")
                    else:
                        logger.info(f"预订请求{reservation_request.id}没有关联的订单项或订单，跳过")
            return False, None
        except Exception as e:
            logger.error(f"检查企业订单失败: {str(e)}")
            return False, None

    def check_enterprise_order_in_day_by_meal_type(self, session: Session, user_id: int, request_date_list: List[str], meal_type) -> bool:
        """
        检查用户在指定日期是否已有同一企业支付成功的指定餐食类型订单

        Args:
            session: 数据库会话
            user_id: 用户ID
            request_date_list: 订单消费日期列表
            meal_type: 餐食类型（MealType枚举）
        Returns:
            tuple: (bool, datetime) 如果已有该企业指定餐食类型支付成功的订单则返回(True, 预订时间)，否则返回(False, None)
        """
        try:
            from app.models.rule import RuleItem

            # 获取当前日期
            today = datetime.now().date()
            # 计算前7天的日期
            seven_days_before = today - timedelta(days=7)
            seven_days_before_start = datetime.combine(seven_days_before, time.min)

            # 查询用户前七天内已支付成功的预订请求，并关联规则项以获取餐食类型
            reservation_requests = session.query(ReservationRequest).join(
                RuleItem, ReservationRequest.rule_item_id == RuleItem.id
            ).filter(
                ReservationRequest.user_id == user_id,
                ReservationRequest.status == ReservationStatus.PAID_FULL,
                ReservationRequest.created_at >= seven_days_before_start,
                RuleItem.meal_type == meal_type  # 过滤特定餐食类型
            ).all()

            logger.info(f"用户{user_id}前七天内{meal_type.value if meal_type else '未知'}类型预订订单数量: {len(reservation_requests)}")

            for reservation_request in reservation_requests:
                reservation_period = reservation_request.reservation_period
                reservation_period_start = reservation_period.split("_")[0]
                reservation_period_start = '20'+reservation_period_start[0:6]
                reservation_period_start = datetime.strptime(reservation_period_start, '%Y%m%d')
                logger.info(f"预订订单{reservation_request.id}的预订时间: {reservation_period_start}")

                # 商务餐不占用员工自助餐名额
                if reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
                    logger.info(f"预订订单{reservation_request.id}是商务餐预订，跳过")
                    continue

                if reservation_period_start.date() in request_date_list:
                    logger.info(f"预订订单{reservation_period_start.date()}的预订时间在{request_date_list}中")
                    # 查询该预订订单的支付类型，区分如果非企业账户支付就跳过
                    order_item = reservation_request.order_item
                    if order_item and order_item.order:
                        order_object = order_item.order
                        if order_object and order_object.payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
                            logger.info(f"订单{order_object.order_no}是企业账户支付，餐食类型: {meal_type.value if meal_type else '未知'}")
                            return True, reservation_period_start
                        else:
                            logger.info(f"订单{order_object.order_no if order_object else '未知'}不是企业账户支付，跳过")
                    else:
                        logger.info(f"预订请求{reservation_request.id}没有关联的订单项或订单，跳过")
            return False, None
        except Exception as e:
            logger.error(f"检查企业订单失败: {str(e)}")
            return False, None

    def update_split_payment_info(self, session: Session, order_id: int, enterprise_amount: float, personal_amount: float) -> Optional[Order]:
        """
        更新订单的分次支付信息

        Args:
            session: 数据库会话
            order_id: 订单ID
            enterprise_amount: 企业支付金额
            personal_amount: 个人需要支付的金额

        Returns:
            Optional[Order]: 更新后的订单对象，如果失败返回None
        """
        try:
            order = self.get(session, order_id)
            if not order:
                logger.error(f"订单不存在: {order_id}")
                return None

            # 更新分次支付相关字段（但不更改支付状态，让pay_order_partially来处理）
            order.requires_personal_payment = round(personal_amount, 2)  # 处理浮点数精度问题
            order.is_split_payment = True
            order.updated_at = datetime.now()

            session.commit()
            logger.info(f"订单 {order_id} 分次支付信息更新成功，个人待支付: {personal_amount}")

            # 重新获取更新后的订单
            order = self.get(session, order_id)
            return order

        except Exception as e:
            logger.error(f"更新订单分次支付信息失败: {str(e)}")
            session.rollback()
            return None

class OrderItemDAO(DAO):
    """订单项DAO类"""

    def __init__(self):
        super().__init__(OrderItem)

    def create(self, session: Session, order_item: OrderItemCreate) -> OrderItem:
        """创建订单项"""
        order_item_data = order_item.model_dump()
        return super().create(session, **order_item_data)

    def get(self, session: Session, order_item_id: int) -> Optional[OrderItem]:
        """获取订单项"""
        return super().get(session, order_item_id)

    def get_list(self, session: Session, skip: int = 0, limit: int = 100) -> List[OrderItem]:
        """获取订单项列表"""
        return session.query(self.model).offset(skip).limit(limit).all()

    def update(self, session: Session, order_item_id: int, order_item: OrderItemUpdate) -> Optional[OrderItem]:
        """更新订单项"""
        order_item_data = order_item.model_dump(exclude_unset=True)
        return super().update(session, order_item_id, **order_item_data)

    def delete(self, session: Session, order_item_id: int) -> bool:
        """删除订单项"""
        return super().delete(session, order_item_id)
    
    def get_by_order(self, session: Session, order_id: int) -> List[OrderItem]:
        """获取订单的所有订单项"""
        return session.query(self.model).filter(self.model.order_id == order_id).all()

    def update_status(self, session: Session, order_id: int, status: OrderStatus) -> Optional[OrderItem]:
        """更新订单状态"""
        order_items = session.query(self.model).filter(self.model.order_id == order_id).all()
        for order_item in order_items:
            order_item.status = status
            session.commit()
        return order_items

    def get_by_order_item_id(self, session: Session, order_item_id: int) -> Optional[OrderItem]:
        """根据订单项ID获取订单项"""
        return session.query(self.model).filter(self.model.id == order_item_id).first()


# 创建DAO实例
order_dao = OrderDAO()
order_item_dao = OrderItemDAO()
