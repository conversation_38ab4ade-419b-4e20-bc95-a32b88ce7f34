from datetime import datetime, timedelta, time
from typing import List, Tuple, Dict, Any
import json
import os

from croniter import croniter
from sqlalchemy.orm import Session

from app.dao.reservation import reservation_request_dao
from app.dao.rule import rule_dao, rule_item_dao
from app.models.reservation import ReservationStatus
from app.models.rule import RuleType
from app.models.product import ProductType
from app.service.pricing import pricing_service
from app.utils.logger import logger


class ReservationService:

    @staticmethod
    def is_can_reserved(
            db: Session,
            rule_item_ids: List[int],
            product_id: int
    ) -> Tuple[bool, str]:
        """
        检查是否可以预约
        
        Args:
            db: 数据库会话
            rule_item_ids: 规则项ID列表
            product_id: 产品ID
            
        Returns:
            Tuple[bool, str]: (是否可预约, 消息)
        """
        # 1. 获取所有传入的规则项对应的规则
        rules_checked = set()
        current_time = datetime.now()

        # 检查每个规则项
        for rule_item_id in rule_item_ids:
            # 获取规则项
            rule_item = rule_item_dao.get(db, rule_item_id)
            if not rule_item:
                return False, f"规则项 {rule_item_id} 不存在"

            rule_id = rule_item.rule_id

            # 如果规则已经检查过，则跳过
            if rule_id in rules_checked:
                continue

            # 获取规则
            rule = rule_dao.get(db, rule_id)
            if not rule:
                return False, f"规则 {rule_id} 不存在"

            # 检查规则是否绑定了传入的产品
            product_ids = [p.id for p in rule.products]
            if product_id not in product_ids:
                return False, f"规则 {rule.name} 未绑定产品 {product_id}"

            # 检查规则项是否在可预订时间范围内
            if rule_item.start_time and rule_item.end_time:
                if not (rule_item.start_time <= current_time <= rule_item.end_time):
                    return False, f"规则项 {rule_item.name} 不在可预订时间范围内"

            # 标记规则已检查
            rules_checked.add(rule_id)

        # 2. 检查每个规则项的预约数量是否超过限制
        for rule_item_id in rule_item_ids:
            rule_item = rule_item_dao.get(db, rule_item_id)

            # 获取该规则项下有效的预约请求
            valid_statuses = [
                ReservationStatus.PENDING,
                ReservationStatus.PAID_DEPOSIT,
                ReservationStatus.PAID_FULL
            ]

            # 查询有效的预约请求数
            reservation_requests = db.query(reservation_request_dao.model).filter(
                reservation_request_dao.model.product_id == product_id,
                reservation_request_dao.model.rule_item_id == rule_item_id,
                reservation_request_dao.model.status.in_(valid_statuses)
            ).all()

            # 计算有效预约数
            valid_reservation_count = len(reservation_requests)

            # 检查是否超过限制
            if valid_reservation_count >= rule_item.quantity:
                return False, f"规则项 {rule_item.name} 的预约数量已达上限"

        # 全部检查通过
        return True, "可以预约"

    @staticmethod
    def _load_holidays() -> set:
        """
        加载节假日数据

        Returns:
            set: 节假日日期集合 (YYYY-MM-DD格式)
        """
        try:
            holiday_file_path = os.path.join(os.getcwd(), 'holiday.json')
            if os.path.exists(holiday_file_path):
                with open(holiday_file_path, 'r', encoding='utf-8') as f:
                    holidays = json.load(f)
                    return set(holidays)
            return set()
        except Exception as e:
            logger.warning(f"加载节假日文件失败: {e}")
            return set()

    @staticmethod
    def get_next_execution_times(cron_string: str, start_time: datetime, num_times: int = 1) -> List[datetime]:
        """
        获取下几次执行时间，跳过节假日

        Args:
            cron_string: Cron表达式字符串
            start_time: 起始时间
            num_times: 获取的执行次数
        Returns:
            List[datetime]: 下几次执行时间列表，确保返回num_times个时间
        """
        # 加载节假日数据
        holidays = ReservationService._load_holidays()

        iter = croniter(cron_string, start_time)
        next_times = []
        # 确保 num_times 不为 None，如果为 None 则使用默认值 1
        num_times = 5 if num_times is None else num_times

        # 如果没有节假日文件，使用原来的逻辑
        if not holidays:
            for _ in range(num_times):
                next_time = iter.get_next(datetime)
                next_times.append(next_time)
            return next_times

        # 有节假日文件时，跳过节假日
        # 设置最大尝试次数，防止无限循环（比如所有日期都是节假日的极端情况）
        max_attempts = num_times * 10  # 最多尝试10倍的次数
        attempts = 0

        while len(next_times) < num_times and attempts < max_attempts:
            next_time = iter.get_next(datetime)
            attempts += 1

            # 检查该日期是否为节假日
            date_str = next_time.strftime("%Y-%m-%d")
            if date_str not in holidays:
                next_times.append(next_time)
            # 如果是节假日，跳过这个时间，继续获取下一个

        # 如果经过最大尝试次数后仍然没有获得足够的时间，记录警告并返回已获得的时间
        if len(next_times) < num_times:
            logger.warning(f"经过{max_attempts}次尝试，只获得了{len(next_times)}个非节假日时间，期望{num_times}个")

        return next_times

    @staticmethod
    def get_availability_list(session, rule_id):
        rule = rule_dao.get(session, rule_id)
        if not rule:
            raise ValueError(f"规则ID {rule_id} 不存在")
        if rule.type != RuleType.RESERVATION:
            raise ValueError(f"规则ID {rule_id} 不是可预订规则")
        rule_items = rule_item_dao.get_by_rule(session, rule_id)
        result = []
        for rule_item in rule_items:
            now = datetime.now()
            # 计算下一次执行时间
            start_times = ReservationService.get_next_execution_times(
                rule_item.start_time_cron_str,
                now,
                num_times=rule_item.generated_count
            )
            end_times = ReservationService.get_next_execution_times(
                rule_item.end_time_cron_str,
                start_times[0],
                num_times=rule_item.generated_count
            )
            for start, end in zip(start_times, end_times):
                period = start.strftime("%y%m%d%H%M") + "_" + end.strftime("%y%m%d%H%M")
                result.append({"rule_id": rule.id, "rule_item_id": rule_item.id, "reservation_period": period})
        return result


    @staticmethod
    def wx_miniapp_get_availability_list(session, rule_id, source, meal_type, qr_type) -> List[Dict[str, Any]]:
        """
        微信小程序获取可预订信息

        Args:
            session: 数据库会话
            rule_id: 规则ID
            source: 请求来源（topic或admin）
            meal_type: 餐食类型
            qr_type: 二维码类型
        Returns:
            List[Dict[str, Any]]: 可预订信息列表
        """

        rule = rule_dao.get(session, rule_id)
        if not rule:
            raise ValueError(f"规则ID {rule_id} 不存在")
        if rule.type != RuleType.RESERVATION:
            raise ValueError(f"规则ID {rule_id} 不是可预订规则")

        # 获取规则关联的产品列表
        products = rule_dao.get_products_by_rule(session, rule_id)

        rule_items = rule_item_dao.get_by_rule(session, rule_id)
        result = []
        
        now = datetime.now()
        today = now.date()

        if source == "admin" and qr_type == "static":
            add_price = 3.0
        else:
            add_price = 0.0

        for rule_item in rule_items:
            # 处理未来的时间段
            generated_count = 5 if rule_item.generated_count is None else rule_item.generated_count

            # 获取更多的时间段以确保有足够的有效时间段
            # 如果source为topic，需要获取更多时间段来应对过滤
            extra_times = generated_count * 2 if source == "topic" else generated_count

            start_times = ReservationService.get_next_execution_times(
                rule_item.start_time_cron_str,
                now,
                num_times=extra_times
            )
            end_times = ReservationService.get_next_execution_times(
                rule_item.end_time_cron_str,
                start_times[0],
                num_times=extra_times
            )
            
            # 对于管理员，特殊处理当天的数据
            if source == "admin":
                # 解析 cron 表达式
                start_cron_parts = rule_item.start_time_cron_str.split()
                end_cron_parts = rule_item.end_time_cron_str.split()
                
                # 检查 cron 表达式是否适用于今天（星期几）
                weekday = now.weekday() + 1  # croniter 中周一是 1，周日是 7
                if weekday == 7:  # 调整周日为 0
                    weekday = 0
                    
                weekday_part = start_cron_parts[4]
                
                # 检查今天是否在允许的星期几范围内
                is_today_valid = False
                if weekday_part == "*":
                    is_today_valid = True
                else:
                    for day in weekday_part.split(","):
                        if str(weekday) == day:
                            is_today_valid = True
                            break
                
                if is_today_valid:
                    # 今天是允许的星期几，创建今天的时间段
                    start_hour = int(start_cron_parts[1])
                    start_minute = int(start_cron_parts[0])
                    
                    end_hour = int(end_cron_parts[1])
                    end_minute = int(end_cron_parts[0])
                    
                    # 创建今天的开始和结束时间
                    today_start = datetime.combine(today, time(hour=start_hour, minute=start_minute))
                    today_end = datetime.combine(today, time(hour=end_hour, minute=end_minute))
                    
                    # 检查结束时间是否已过
                    if today_end > now:
                        # 结束时间还没到，添加今天的时间段
                        period = today_start.strftime("%y%m%d%H%M") + "_" + today_end.strftime("%y%m%d%H%M")
                        date_str = today_start.strftime("%Y-%m-%d")
                        
                        # 检查今天的时间段是否已经在 start_times 和 end_times 中
                        today_already_included = False
                        for st, et in zip(start_times, end_times):
                            if st.date() == today:
                                today_already_included = True
                                break
                        
                        # 如果今天的时间段不在 start_times 和 end_times 中，则添加
                        if not today_already_included:
                            # 将今天的时间段插入到列表开头
                            start_times.insert(0, today_start)
                            end_times.insert(0, today_end)
            
            # 收集有效的时间段
            valid_items = []

            # 处理所有时间段
            for index, (start, end) in enumerate(zip(start_times, end_times)):
                period = start.strftime("%y%m%d%H%M") + "_" + end.strftime("%y%m%d%H%M")

                # 获取当天日期
                date_str = start.strftime("%Y-%m-%d")

                # 检查日期是否在七天内
                delta = (start.date() - now.date()).days

                logger.info(f"今天是第{delta}天")

                if source == "admin":
                    # 如果source为admin，则不检查预订截止时间
                    logger.info(f"source为admin，不检查预订截止时间")
                else:
                    logger.info(f"source为topic，检查预订截止时间")
                    # 检查预订截止时间
                    if rule_item.order_deadline is not None:
                        # 计算当天预订就餐开始时间
                        deadline_date = start
                        # 将分钟数转换为小时和分钟
                        hours = rule_item.order_deadline // 60
                        minutes = rule_item.order_deadline % 60
                        logger.info(f"hours: {hours}, minutes: {minutes}")
                        # 创建预订的截止时间
                        order_deadline_time = deadline_date - timedelta(hours=hours, minutes=minutes)
                        logger.info(f"当天预订就餐时间: {deadline_date}")
                        logger.info(f"当天预订截止时间: {order_deadline_time}")
                        if now > order_deadline_time:
                            logger.info(f"预订截止时间已过，跳过")
                            continue
                    elif now.hour >= 9 and delta == 0:
                        logger.info(f"当天数据且当前时间已超过早上9点，跳过")
                        continue
                    else:
                        logger.info(f"无需检查预订截止时间")

                # 获取总可预约人数
                total_capacity = rule_item.quantity

                # 获取已预约人数（状态为待支付、已支付定金、已支付全款和已核销的预约）不计入已取消的预约
                valid_statuses = [
                    # ReservationStatus.PENDING,
                    ReservationStatus.PAID_DEPOSIT,
                    ReservationStatus.PAID_FULL,
                    ReservationStatus.VERIFIED
                ]
                # 累加每个预约请求关联的订单项的quantity值，而不是计算预约请求数量
                reserved_count = 0
                reservation_requests = session.query(reservation_request_dao.model).filter(
                    reservation_request_dao.model.rule_item_id == rule_item.id,
                    reservation_request_dao.model.status.in_(valid_statuses)
                ).all()

                logger.info(f"预订请求数量: {len(reservation_requests)}")
                logger.info(f"预订请求: {date_str}")

                for req in reservation_requests:
                    # 获取预订订单的就餐日期
                    reservation_period = req.reservation_period  # 2505091000_2505091200
                    reservation_period_start, reservation_period_end, reservation_time = ReservationService.extract_start_end_times(reservation_period)

                    # 将 reservation_period_start 转换为 YYYY-MM-DD 格式
                    reservation_date = datetime.strptime(reservation_period_start, "%Y-%m-%d %H:%M").strftime("%Y-%m-%d")

                    # 比较日期
                    if date_str == reservation_date:
                        # 日期匹配，继续处理
                        reserved_count += req.order_item.quantity
                    else:
                        continue  # 日期不匹配，跳过当前预约

                # 计算剩余可预约人数
                available_capacity = max(0, total_capacity - reserved_count)
                if available_capacity <= 0:
                    remaining = "已满"
                    remaining_int = 0
                    disabled = True
                else:
                    remaining = f"剩余{available_capacity}位"
                    remaining_int = available_capacity
                    disabled = False

                # 添加价格信息
                prices = []
                for product in products:
                    # 获取产品的基本价格
                    price = product.price
                    try:
                        reservation_fee = getattr(product, 'reservation_fee', 0)
                    except:
                        reservation_fee = 0
                    if hasattr(product, 'type') and product.type == ProductType.RESERVATION and hasattr(product, 'reservation_fee'):
                        # 计算产品优惠价格
                        unit_price, total_price, _ = pricing_service.product_pricing(session, product.id, price, 1, add_price)
                        prices.append({
                            "product_id": product.id,
                            "product_name": product.name,
                            "original_price": price,
                            "reservation_fee": reservation_fee,
                            "final_price": unit_price,
                            "total_price": total_price
                        })

                # 获取最便宜的应付价格
                min_price_product = min(prices, key=lambda p: p["total_price"])
                affordable_price = min_price_product["total_price"]
                affordable_product_id = min_price_product["product_id"]

                # 添加预约时间段
                start_time, end_time, reservation_time = ReservationService.extract_start_end_times(period)

                item = {
                    "rule_id": rule.id,
                    "rule_item_id": rule_item.id,
                    "rule_meal_type": rule_item.meal_type,
                    "reservation_period": period,
                    "date": date_str,
                    "total_capacity": total_capacity,
                    "reserved_count": reserved_count,
                    "available_capacity": available_capacity,
                    "products": prices,
                    "start_time": start_time,
                    "end_time": end_time,
                    "time": reservation_time,
                    "remaining": remaining,
                    "remaining_int": remaining_int,
                    "disabled": disabled,
                    "can_select": True,
                    "price": affordable_price,
                    "product_id": affordable_product_id
                }

                # 将有效的时间段添加到列表中
                valid_items.append(item)

                # 如果已经收集到足够的时间段，就停止
                if len(valid_items) >= generated_count:
                    break

            # 将有效的时间段添加到结果中
            result.extend(valid_items)
            logger.info(f"提取到的可预订时间段: {result}")

        return result

    @staticmethod
    def extract_start_end_times(reservation_period):
        """
        提取开始和结束时间

        Args:
            reservation_period: 预约时间段
        Returns:
            Tuple[datetime, datetime, str]: 开始时间, 结束时间, 预约时间段
        """
        # 分离开始和结束时间
        time_parts = reservation_period.split('_')

        # 解析开始时间
        start_str = time_parts[0]
        start_year = '20' + start_str[:2]
        start_month = start_str[2:4]
        start_day = start_str[4:6]
        start_hour = start_str[6:8]
        start_minute = start_str[8:10]

        # 解析结束时间
        end_str = time_parts[1]
        end_year = '20' + end_str[:2]
        end_month = end_str[2:4]
        end_day = end_str[4:6]
        end_hour = end_str[6:8]
        end_minute = end_str[8:10]

        # 格式化开始和结束时间
        start_time = f"{start_year}-{start_month}-{start_day} {start_hour}:{start_minute}"
        end_time = f"{end_year}-{end_month}-{end_day} {end_hour}:{end_minute}"
        reservation_time = f"{start_hour}:{start_minute}-{end_hour}:{end_minute}"

        return start_time, end_time, reservation_time


reservation_service = ReservationService()
