import hashlib
import json
import time
from datetime import datetime
from typing import Dict, Any
from typing import Optional
import logging

import requests
from pydantic import BaseModel, Field
from redis import Redis

from app.core.config import settings

# 配置日志
logger = logging.getLogger(__name__)


class GetTokenRequest(BaseModel):
    """获取token请求参数"""
    grant_type: str = Field("authorization_code", description="授权模式")
    code: str = Field(..., description="授权码")
    app_id: str = Field(..., description="应用ID")
    merchant_id: str = Field(..., description="商户ID")
    signature: Optional[str] = Field(None, description="签名")
    timestamp: int = Field(..., description="时间戳")


# 初始化Redis客户端
redis_client = Redis.from_url(settings.REDIS_URL)


class FengniaoClient:
    """蜂鸟即配API客户端"""

    def __init__(self, app_key: Optional[str] = None, app_secret: Optional[str] = None, api_url: Optional[str] = None):
        self.app_key = app_key or settings.FENGNIAO_APP_KEY
        self.app_secret = app_secret or settings.FENGNIAO_APP_SECRET
        self.api_url = api_url or settings.FENGNIAO_API_URL
        self.merchant_id = None or settings.FENGNIAO_MERCHANT_ID

        if not self.app_key or not self.app_secret:
            raise ValueError("蜂鸟即配的AppKey和AppSecret不能为空")

    def _generate_sign(self, params: Dict[str, Any]) -> str:
        """生成签名"""
        # 1. 对参数按key进行ASCII排序
        sorted_params = sorted(params.items(), key=lambda x: x[0])

        # 2. 拼接为"key=value&key=value"格式
        sign_list = []
        for key, value in sorted_params:
            sign_list.append('{}={}'.format(key, value))
        sign_before = self.app_secret + '&'.join(sign_list)
        # print ('signBefore:{} \n'.format(sign_before))

        # 3. HMAC-SHA256加密 + 转大写
        sha256 = hashlib.sha256()
        sha256.update(sign_before.encode('utf-8'))
        sign = sha256.hexdigest()
        # print ('sign:{} \n'.format(sign))
        return sign

    def _request(self, path: str, method: str = "POST", **kwargs) -> Dict[str, Any]:
        """发送请求到蜂鸟API"""
        # 1. 公共参数
        common_params = {
            "app_id": self.app_key,
            "timestamp": int(time.time() * 1000)  # 毫秒级时间戳
        }

        # 2. 合并参数
        params = {**common_params, **kwargs}

        # 3. 生成签名
        sign = self._generate_sign(params)
        params["signature"] = sign

        params = json.dumps(params)
        # print ('params:{} \n'.format(params))

        # 4. 发送请求
        url = f"{self.api_url}{path}"
        # print ('url: {} \n'.format(url))
        try:
            if method.upper() == "GET":
                response = requests.get(url, params=params, timeout=10)
            else:
                response = requests.post(url, data=params, timeout=10)

            # 5. 解析响应
            return response.json()
        except Exception as e:
            return {"success": False, "code": -1, "msg": f"请求异常: {str(e)}"}

    def get_token(self, token_request: GetTokenRequest):
        """获取token（优先从redis读取）"""
        # 获取数据库会话
        try:
            # 1. 从redis查询是否存在有效token
            current_time = datetime.now()
            token_str = redis_client.get(f"fengniao:token")
            token_dict = json.loads(token_str) if token_str else {}
            # print (f'token_dict: {token_dict}')

            if token_dict and token_dict.get("access_token"):
                # 2. 如果存在有效token，直接返回
                return {
                    "success": True,
                    "business_data": json.dumps(token_dict)
                }

            # 3. 如果不存在，调用外部接口获取token
            req_data = token_request.dict(exclude_none=True)
            response = self._request("/anubis-webapi/openapi/token", **req_data)

            # 4. 将新token保存到数据库
            if response.get("success"):
                business_data = json.loads(response.get("business_data", '{}'))
                redis_client.set(
                    f"fengniao:token",
                    json.dumps(business_data),
                    ex=business_data.get("expire_in", 3600)
                )

            return response
        except Exception as e:
            return {"success": False, "code": -1, "msg": f"请求异常: {str(e)}"}

    def invoke(self, invoke_type: str, business_data: dict):
        """业务请求接口"""
        access_token = None
        token_request = GetTokenRequest(
            grant_type="authorization_code",
            code=settings.FENGNIAO_AUTHORIZATION_CODE,
            app_id=settings.FENGNIAO_APP_KEY,
            merchant_id=settings.FENGNIAO_MERCHANT_ID,
            timestamp=int(time.time() * 1000)
        )
        token_resp = self.get_token(token_request)

        if token_resp.get('success'):
            token_data = json.loads(token_resp.get('business_data', '{}'))
            access_token = token_data.get('access_token')

        request_data = {
            "access_token": access_token,
            "merchant_id": self.merchant_id,
            "business_data": json.dumps(business_data, ensure_ascii=False),
            "version": "1.0"
        }

        # 调用API
        response = self._request("/anubis-webapi/v3/invoke/{}".format(invoke_type), **request_data)

        # 转换为响应模型
        return response

    # ========== 订单管理接口方法 ==========
    
    def pre_create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """预下单接口调用示例"""
        logger.info("调用预下单接口")
        logger.debug(f"请求参数: {json.dumps(order_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("preCreateOrder", order_data)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印预下单接口的返回参数
        self._parse_pre_create_order_response(result)
        
        return result
    
    def _parse_pre_create_order_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印预下单接口的返回参数"""
        logger.info("预下单接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                logger.info(f"配送距离 (distance): {business_data.get('distance', 'N/A')} 米")
                logger.info(f"预询系统时间戳 (time): {business_data.get('time', 'N/A')} 毫秒")
                logger.debug(f"备注 (remark): {business_data.get('remark', 'N/A')}")
                
                # 解析可用运力列表
                goods_infos = business_data.get('goods_infos', [])
                if goods_infos:
                    logger.info(f"可用运力列表 (goods_infos) - 共 {len(goods_infos)} 个商品")
                    
                    for i, goods_info in enumerate(goods_infos):
                        logger.info(f"第 {i+1} 个商品:")
                        logger.info(f"  基础商品ID (base_goods_id): {goods_info.get('base_goods_id', 'N/A')}")
                        logger.info(f"  服务商品ID (service_goods_id): {goods_info.get('service_goods_id', 'N/A')}")
                        logger.info(f"  预询标识 (t_index_id): {goods_info.get('t_index_id', 'N/A')}")
                        logger.info(f"  是否可用 (is_valid): {goods_info.get('is_valid', 'N/A')} (0不可用，1可用)")
                        logger.debug(f"  商品介绍 (slogan): {goods_info.get('slogan', 'N/A')}")
                        logger.debug(f"  不可用原因 (disable_reason): {goods_info.get('disable_reason', 'N/A')}")
                        logger.info(f"  是否支持加小费 (can_add_tip): {goods_info.get('can_add_tip', 'N/A')} (0否，1是)")
                        logger.info(f"  预计送达时间 (predict_delivery_time): {goods_info.get('predict_delivery_time', 'N/A')} 毫秒")
                        logger.info(f"  预计送达时间 (predict_delivery_minutes): {goods_info.get('predict_delivery_minutes', 'N/A')} 分钟")
                        logger.info(f"  原始配送费总价格 (total_delivery_amount_cent): {goods_info.get('total_delivery_amount_cent', 'N/A')} 分")
                        logger.info(f"  优惠后配送费总价格 (actual_delivery_amount_cent): {goods_info.get('actual_delivery_amount_cent', 'N/A')} 分 ⭐重要⭐")
                        logger.debug(f"  优惠券记录ID (warehouse_id): {goods_info.get('warehouse_id', 'N/A')}")
                        
                        # 解析价格详情
                        price_detail = goods_info.get('price_detail', {})
                        if price_detail:
                            logger.debug("  价格详情 (price_detail)")
                            logger.debug(f"    起送价 (start_price_cent): {price_detail.get('start_price_cent', 'N/A')} 分")
                            logger.debug(f"    距离加价 (distance_price_cent): {price_detail.get('distance_price_cent', 'N/A')} 分")
                            logger.debug(f"    重量加价 (weight_price_cent): {price_detail.get('weight_price_cent', 'N/A')} 分")
                            logger.debug(f"    时段加价 (time_period_surcharge_cent): {price_detail.get('time_period_surcharge_cent', 'N/A')} 分")
                            logger.debug(f"    运力紧张加价 (pressure_surcharge_cent): {price_detail.get('pressure_surcharge_cent', 'N/A')} 分")
                            logger.debug(f"    跨江单加价 (river_crossing_surcharge_cent): {price_detail.get('river_crossing_surcharge_cent', 'N/A')} 分")
                            logger.debug(f"    品类加价 (category_surcharge_cent): {price_detail.get('category_surcharge_cent', 'N/A')} 分")
                            logger.debug(f"    临时加价 (temporary_surcharge_cent): {price_detail.get('temporary_surcharge_cent', 'N/A')} 分")
                            logger.debug(f"    客单价加价 (order_price_surcharge_cent): {price_detail.get('order_price_surcharge_cent', 'N/A')} 分")
                else:
                    logger.warning("无可用运力")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
            
            # 根据文档提供的错误码说明
            error_code_descriptions = {
                'B0103': '运单预计送达时间不能早于当前时间',
                'B0105': '运单运力紧张，请稍后重试',
                'B0106': '当前区域无运力',
                'B0112': '门店不存在，请登录官网后台查看门店状态信息',
                'B0114': '压力平衡关店',
                'B0202': '门店已冻结',
                'B0208': '商户认证未通过，请联系管理员解决'
            }
            
            error_code = response.get('code', '')
            if error_code in error_code_descriptions:
                logger.error(f"错误码说明: {error_code_descriptions[error_code]}")
    
    def create_order(self, order_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建订单接口调用示例"""
        logger.info("调用创建订单接口")
        logger.info(f"请求参数: {json.dumps(order_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("createOrder", order_data)
        
        logger.info(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印创建订单接口的返回参数
        self._parse_create_order_response(result)
        
        return result
    
    def _parse_create_order_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印创建订单接口的返回参数"""
        logger.info("创建订单接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                logger.info(f"订单ID (order_id): {business_data.get('order_id', 'N/A')} ⭐重要⭐")
                
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
            
            # 根据文档提供的错误码说明
            error_code_descriptions = {
                'B0102': '运单超出配送范围，暂不支持配送',
                'B0103': '运单预计送达时间不能早于当前时间',
                'B0105': '运单运力紧张，请稍后重试',
                'B0106': '当前区域无运力',
                'B0107': '资金余额不足',
                'B0109': '运单价格不一致',
                'B0114': '压力平衡关店',
                'B0202': '门店已冻结',
                'B0111': '运单不存在'
            }
            
            error_code = response.get('code', '')
            if error_code in error_code_descriptions:
                logger.error(f"错误码说明: {error_code_descriptions[error_code]}")
    
    def get_order_detail(self, order_query: Dict[str, Any]) -> Dict[str, Any]:
        """查询订单详情接口调用示例"""
        logger.info("调用查询订单详情接口")
        logger.debug(f"请求参数: {json.dumps(order_query, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("getOrderDetail", order_query)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印订单详情接口的返回参数
        self._parse_order_detail_response(result)
        
        return result
    
    def _parse_order_detail_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印订单详情接口的返回参数"""
        logger.info("查询订单详情接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("订单基本信息")
                logger.info(f"外部订单号 (partner_order_code): {business_data.get('partner_order_code', 'N/A')}")
                logger.info(f"订单ID (order_id): {business_data.get('order_id', 'N/A')}")
                logger.info(f"订单状态 (order_status): {business_data.get('order_status', 'N/A')}")
                
                # 订单状态说明
                status_desc = {
                    0: "订单生成", 1: "运单生成成功", 20: "骑手接单", 
                    80: "骑手到店", 2: "配送中", 3: "已完成", 
                    4: "已取消", 5: "配送异常"
                }
                order_status = business_data.get('order_status')
                if order_status is not None:
                    logger.info(f"订单状态说明: {status_desc.get(order_status, '未知状态')}")
                
                logger.info(f"配送距离 (order_distance): {business_data.get('order_distance', 'N/A')} 米")
                logger.info(f"预计送达时间 (estimate_arrive_time): {business_data.get('estimate_arrive_time', 'N/A')} 毫秒")
                logger.debug(f"备注 (remark): {business_data.get('remark', 'N/A')}")
                logger.debug(f"商家订单小号 (serial_number): {business_data.get('serial_number', 'N/A')}")
                logger.debug(f"取货码 (fetch_code): {business_data.get('fetch_code', 'N/A')}")
                logger.debug(f"收货码 (write_off_code): {business_data.get('write_off_code', 'N/A')}")
                
                logger.info("配送费用信息")
                logger.info(f"原始配送费 (order_total_amount_cent): {business_data.get('order_total_amount_cent', 'N/A')} 分")
                logger.info(f"实际配送费 (order_actual_amount_cent): {business_data.get('order_actual_amount_cent', 'N/A')} 分 ⭐重要⭐")
                logger.info(f"订单当前小费总金额 (order_tip_amount_cent): {business_data.get('order_tip_amount_cent', 'N/A')} 分")
                logger.debug(f"时效赔付 (overtime_compensation_cost_cent): {business_data.get('overtime_compensation_cost_cent', 'N/A')} 分")
                logger.debug(f"是否支持添加调度费 (if_can_add_tip): {business_data.get('if_can_add_tip', 'N/A')} (1可以，0不可以)")
                
                logger.info("配送员信息")
                logger.info(f"配送员ID (carrier_driver_id): {business_data.get('carrier_driver_id', 'N/A')}")
                logger.info(f"配送员姓名 (carrier_driver_name): {business_data.get('carrier_driver_name', 'N/A')}")
                logger.info(f"配送员电话 (carrier_driver_phone): {business_data.get('carrier_driver_phone', 'N/A')}")
                logger.debug(f"配送员体温 (temperature): {business_data.get('temperature', 'N/A')}")
                
                # 记录异常、投诉、索赔信息（如果存在）
                if business_data.get('abnormal_code') or business_data.get('abnormal_desc'):
                    logger.warning("异常信息")
                    logger.warning(f"运单异常原因code (abnormal_code): {business_data.get('abnormal_code', 'N/A')}")
                    logger.warning(f"运单异常原因描述 (abnormal_desc): {business_data.get('abnormal_desc', 'N/A')}")
                
                if business_data.get('complaint_id'):
                    logger.info("投诉信息")
                    logger.info(f"投诉编号 (complaint_id): {business_data.get('complaint_id', 'N/A')}")
                    logger.info(f"投诉原因描述 (complaint_reason_desc): {business_data.get('complaint_reason_desc', 'N/A')}")
                    logger.info(f"投诉状态 (complaint_status): {business_data.get('complaint_status', 'N/A')} (1待处理，2成功，3失败)")
                    logger.debug(f"投诉失败原因 (complaint_response_desc): {business_data.get('complaint_response_desc', 'N/A')}")
                
                if business_data.get('claim_id'):
                    logger.info("索赔信息")
                    logger.info(f"索赔ID (claim_id): {business_data.get('claim_id', 'N/A')}")
                    logger.info(f"索赔原因描述 (claim_reason_desc): {business_data.get('claim_reason_desc', 'N/A')}")
                    logger.info(f"索赔状态 (claim_status): {business_data.get('claim_status', 'N/A')} (1待处理，2成功，3失败)")
                    logger.debug(f"索赔失败原因 (claim_status_desc): {business_data.get('claim_status_desc', 'N/A')}")
                
                # 记录其他信息
                logger.debug("其他信息")
                logger.debug(f"trackingId (tracking_id): {business_data.get('tracking_id', 'N/A')}")
                logger.debug(f"退货码 (cancel_code): {business_data.get('cancel_code', 'N/A')}")
                
                # 解析价格详情
                price_detail = business_data.get('price_detail', {})
                if price_detail:
                    logger.debug("价格详情 (price_detail)")
                    for key, value in price_detail.items():
                        logger.debug(f"  {key}: {value}")
                
                # 解析运单事件节点信息
                event_log_details = business_data.get('event_log_details', [])
                if event_log_details:
                    logger.info(f"运单事件节点信息 (event_log_details) - 共 {len(event_log_details)} 个事件")
                    for i, event in enumerate(event_log_details):
                        logger.info(f"  事件 {i+1}: 状态{event.get('order_status')} 时间{event.get('occur_time')}")
                
                # 解析骑手取货照片
                delivery_fetch_photos = business_data.get('delivery_fetch_photos', [])
                if delivery_fetch_photos:
                    logger.info(f"骑手取货照片 (delivery_fetch_photos) - 共 {len(delivery_fetch_photos)} 张")
                
                # 解析天气信息
                weather_info = business_data.get('weather_info')
                if weather_info:
                    logger.debug(f"天气信息: 等级{weather_info.get('weatherRate')} 描述{weather_info.get('weatherDesc')}")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
    
    def get_cancel_reason_list(self, order_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """获取取消原因列表接口调用示例"""
        logger.info("调用获取取消原因列表接口")
        
        # 如果没有提供订单信息，使用空字典
        request_data = order_info or {}
        logger.debug(f"请求参数: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("getCancelReasonList", request_data)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印获取取消原因列表接口的返回参数
        self._parse_cancel_reason_list_response(result)
        
        return result
    
    def _parse_cancel_reason_list_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印获取取消原因列表接口的返回参数"""
        logger.info("获取取消原因列表接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                cancel_reason_list = business_data.get('cancel_reason_list', [])
                
                if cancel_reason_list:
                    logger.info(f"取消原因列表 (cancel_reason_list) - 共 {len(cancel_reason_list)} 个原因:")
                    
                    for i, reason in enumerate(cancel_reason_list):
                        logger.info(f"  原因 {i+1}: code={reason.get('order_cancel_code', 'N/A')} desc={reason.get('order_cancel_desc', 'N/A')}")
                        
                        # 特殊说明
                        cancel_code = reason.get('order_cancel_code')
                        if cancel_code == 0:
                            logger.warning(f"    注意: 此原因需要填写补充说明")
                else:
                    logger.warning("取消原因列表 (cancel_reason_list): 无可用的取消原因")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
            
            # 根据文档提供的错误码说明
            error_code_descriptions = {
                'B0108': '运单异常，请联系小二反馈',
                'B0111': '运单超出配送范围，超配送范围，暂不支持配送',
                '00001': '蜂鸟系统异常，请联系小二解决'
            }
            
            error_code = response.get('code', '')
            if error_code in error_code_descriptions:
                logger.error(f"错误码说明: {error_code_descriptions[error_code]}")
    
    def pre_cancel_order(self, cancel_data: Dict[str, Any]) -> Dict[str, Any]:
        """预取消订单接口调用示例"""
        logger.info("调用预取消订单接口")
        logger.debug(f"请求参数: {json.dumps(cancel_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("preCancelOrder", cancel_data)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印预取消订单接口的返回参数
        self._parse_pre_cancel_order_response(result)
        
        return result
    
    def _parse_pre_cancel_order_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印预取消订单接口的返回参数"""
        logger.info("预取消订单接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                logger.info(f"取消实际需金额 (actual_cancel_cost_cent): {business_data.get('actual_cancel_cost_cent', 'N/A')} 分 ⭐重要⭐")
                
                # 金额说明
                cancel_cost = business_data.get('actual_cancel_cost_cent')
                if cancel_cost is not None:
                    if cancel_cost == 0:
                        logger.info("说明: 使用已有免责取消权益，取消费用为0")
                    else:
                        logger.info(f"说明: 取消此订单需要支付 {cancel_cost} 分的取消费用")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
            
            # 根据文档提供的错误码说明
            error_code_descriptions = {
                'B0108': '运单状态不允许取消',
                'B0110': '运单已取消',
                'B0111': '运单不存在',
                'A0100': '参数格式不正确，请查看文档对于参数的说明',
                'A0101': '缺少必填参数，请查看文档的必填参数',
                '00001': '蜂鸟系统异常，请联系小二解决'
            }
            
            error_code = response.get('code', '')
            if error_code in error_code_descriptions:
                logger.error(f"错误码说明: {error_code_descriptions[error_code]}")
    
    def cancel_order(self, cancel_data: Dict[str, Any]) -> Dict[str, Any]:
        """正式取消订单接口调用示例"""
        logger.info("调用正式取消订单接口")
        logger.debug(f"请求参数: {json.dumps(cancel_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("cancelOrder", cancel_data)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印正式取消订单接口的返回参数
        self._parse_cancel_order_response(result)
        
        return result
    
    def _parse_cancel_order_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印正式取消订单接口的返回参数"""
        logger.info("正式取消订单接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                logger.info(f"取消结果 (result): {business_data.get('result', 'N/A')} ⭐重要⭐")
                logger.info(f"逆向单结果 (reverse_order_ok): {business_data.get('reverse_order_ok', 'N/A')}")
                
                # 结果说明
                result = business_data.get('result')
                if result is True:
                    logger.info("订单取消成功")
                elif result is False:
                    logger.error("订单取消失败")
                
                reverse_result = business_data.get('reverse_order_ok')
                if reverse_result is not None:
                    if reverse_result is True:
                        logger.info("逆向单处理成功")
                    else:
                        logger.error("逆向单处理失败")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
            
            # 根据文档提供的错误码说明
            error_code_descriptions = {
                'B0108': '运单状态不允许取消',
                'B0110': '运单已取消',
                'B0111': '运单不存在',
                'B0113': '使用免责取消权益失败',
                'A0100': '参数格式不正确，请查看文档对于参数的说明',
                'A0101': '缺少必填参数，请查看文档的必填参数',
                '00001': '蜂鸟系统异常，请联系小二解决'
            }
            
            error_code = response.get('code', '')
            if error_code in error_code_descriptions:
                logger.error(f"错误码说明: {error_code_descriptions[error_code]}")
    
    def add_tip(self, tip_data: Dict[str, Any]) -> Dict[str, Any]:
        """加小费接口调用示例"""
        logger.info("调用加小费接口")
        logger.debug(f"请求参数: {json.dumps(tip_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("addTip", tip_data)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印加小费接口的返回参数
        self._parse_add_tip_response(result)
        
        return result
    
    def _parse_add_tip_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印加小费接口的返回参数"""
        logger.info("加小费接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                logger.info(f"加小费结果 (result): {business_data.get('result', 'N/A')} ⭐重要⭐")
                
                # 结果说明
                result = business_data.get('result')
                if result is True:
                    logger.info("加小费成功")
                elif result is False:
                    logger.error("加小费失败")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
            
            # 根据文档提供的错误码说明
            error_code_descriptions = {
                'B0101': '运单异常，请联系小二反馈',
                'B0115': '小费支付失败',
                'B0116': '订单不支持加小费，该订单的支付方式或服务商品不支持加小费',
                'A0101': '缺少必填参数，请查看文档的必填参数',
                'A0100': '参数格式不正确，请查看文档对于参数的说明'
            }
            
            error_code = response.get('code', '')
            if error_code in error_code_descriptions:
                logger.error(f"错误码说明: {error_code_descriptions[error_code]}")
    
    def get_knight_info(self, order_query: Dict[str, Any]) -> Dict[str, Any]:
        """查询骑手信息接口调用示例"""
        logger.info("调用查询骑手信息接口")
        logger.debug(f"请求参数: {json.dumps(order_query, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("getKnightInfo", order_query)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印查询骑手信息接口的返回参数
        self._parse_knight_info_response(result)
        
        return result
    
    def _parse_knight_info_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印查询骑手信息接口的返回参数"""
        logger.info("查询骑手信息接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("骑手信息 (business_data)")
                logger.info(f"骑手ID (carrier_driver_id): {business_data.get('carrier_driver_id', 'N/A')}")
                logger.info(f"骑手姓名 (carrier_driver_name): {business_data.get('carrier_driver_name', 'N/A')}")
                logger.info(f"骑手电话 (carrier_driver_phone): {business_data.get('carrier_driver_phone', 'N/A')}")
                logger.info(f"骑手位置经度 (carrier_driver_longitude): {business_data.get('carrier_driver_longitude', 'N/A')} (高德经纬度)")
                logger.info(f"骑手位置纬度 (carrier_driver_latitude): {business_data.get('carrier_driver_latitude', 'N/A')} (高德经纬度)")
                
                # 位置信息说明
                longitude = business_data.get('carrier_driver_longitude')
                latitude = business_data.get('carrier_driver_latitude')
                if longitude and latitude:
                    logger.info(f"骑手当前位置: ({longitude}, {latitude})")
                    logger.debug("说明: 沙箱环境的查询参数可能不全，实际环境会返回完整信息")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
    
    def complaint_order(self, complaint_data: Dict[str, Any]) -> Dict[str, Any]:
        """订单投诉接口调用示例"""
        logger.info("调用订单投诉接口")
        logger.debug(f"请求参数: {json.dumps(complaint_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("complaintOrder", complaint_data)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印订单投诉接口的返回参数
        self._parse_complaint_order_response(result)
        
        return result
    
    def _parse_complaint_order_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印订单投诉接口的返回参数"""
        logger.info("订单投诉接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                logger.info(f"投诉结果 (result): {business_data.get('result', 'N/A')} ⭐重要⭐")
                
                # 结果说明
                result = business_data.get('result')
                if result is True:
                    logger.info("投诉提交成功")
                elif result is False:
                    logger.error("投诉提交失败")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")
    
    def claim_order(self, claim_data: Dict[str, Any]) -> Dict[str, Any]:
        """订单索赔接口调用示例"""
        logger.info("调用订单索赔接口")
        logger.debug(f"请求参数: {json.dumps(claim_data, ensure_ascii=False, indent=2)}")
        
        result = self.invoke("claimOrder", claim_data)
        
        logger.debug(f"原始响应结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        # 解析并详细打印订单索赔接口的返回参数
        self._parse_claim_order_response(result)
        
        return result
    
    def _parse_claim_order_response(self, response: Dict[str, Any]) -> None:
        """解析并详细打印订单索赔接口的返回参数"""
        logger.info("订单索赔接口返回参数详细解析")
        
        # 打印基础响应参数
        logger.info(f"接口调用状态码 (code): {response.get('code', 'N/A')}")
        logger.info(f"接口调用消息 (msg): {response.get('msg', 'N/A')}")
        logger.debug(f"API状态码 (apiCode): {response.get('apiCode', 'N/A')}")
        logger.debug(f"签名 (sign): {response.get('sign', 'N/A')}")
        
        # 解析业务数据
        business_data = response.get('business_data')
        if business_data:
            try:
                if isinstance(business_data, str):
                    business_data = json.loads(business_data)
                
                logger.info("业务数据 (business_data)")
                logger.info(f"索赔结果 (result): {business_data.get('result', 'N/A')} ⭐重要⭐")
                logger.info(f"逆向单结果 (reverse_order_ok): {business_data.get('reverse_order_ok', 'N/A')}")
                
                # 结果说明
                result = business_data.get('result')
                if result is True:
                    logger.info("索赔申请成功")
                elif result is False:
                    logger.error("索赔申请失败")
                
                reverse_result = business_data.get('reverse_order_ok')
                if reverse_result is not None:
                    if reverse_result is True:
                        logger.info("逆向单处理成功")
                    else:
                        logger.error("逆向单处理失败")
                    
            except json.JSONDecodeError as e:
                logger.error(f"解析业务数据失败: {e}")
                logger.error(f"原始业务数据: {business_data}")
        else:
            logger.warning("无业务数据返回")
        
        # 检查错误情况
        if response.get('code') != '200':
            logger.error("接口调用失败!")
            logger.error(f"错误码: {response.get('code', 'N/A')}")
            logger.error(f"错误信息: {response.get('msg', 'N/A')}")

