import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, Request, Depends
from sqlalchemy.orm import Session

from app.core.deps import get_db

fengniao_router = APIRouter()
logger = logging.getLogger(__name__)


def _parse_business_data(business_data_str: str) -> Dict[str, Any]:
    """解析business_data字符串"""
    try:
        if isinstance(business_data_str, str):
            return json.loads(business_data_str)
        return business_data_str
    except json.JSONDecodeError as e:
        logger.error(f"解析business_data失败: {e}, 原始数据: {business_data_str}")
        return {}


def _save_callback_to_db(session: Session, callback_data: Dict[str, Any]) -> Optional[int]:
    """保存回调数据到数据库

    Args:
        session: 数据库会话
        callback_data: 回调数据

    Returns:
        回调记录ID，如果保存失败返回None
    """
    try:
        # 延迟导入避免循环导入
        from app.dao.fengniao import fengniao_callback_dao
        # 解析business_data
        business_data = _parse_business_data(callback_data.get("business_data", "{}"))
        callback_type = business_data.get("callback_business_type", "unknown")

        # 转换时间戳为可读格式
        timestamp = callback_data.get("timestamp", 0)
        try:
            timestamp_int = int(timestamp)
            update_time = datetime.fromtimestamp(timestamp_int / 1000)  # 毫秒转秒
        except (ValueError, TypeError):
            update_time = datetime.now()
            logger.warning(f"时间戳转换失败，使用当前时间: {timestamp}")

        # 保存到fengniao_callbacks表
        callback_record_data = {
            "app_id": callback_data.get("app_id", ""),
            "timestamp": timestamp_int if 'timestamp_int' in locals() else 0,
            "update_time": update_time,
            "signature": callback_data.get("signature", ""),
            "business_data": business_data,
            "callback_type": callback_type,
            "raw_data": callback_data
        }

        callback_record = fengniao_callback_dao.create_callback(session, callback_record_data)
        logger.info(f"保存回调记录成功，ID: {callback_record.id}, 类型: {callback_type}")

        return callback_record.id

    except Exception as e:
        logger.error(f"保存回调数据到数据库失败: {e}")
        return None


def _save_order_status_callback(session: Session, callback_id: int, business_data: Dict[str, Any]) -> bool:
    """保存订单状态回调详细信息

    Args:
        session: 数据库会话
        callback_id: 回调记录ID
        business_data: 业务数据

    Returns:
        是否保存成功
    """
    try:
        # 延迟导入避免循环导入
        from app.dao.fengniao import fengniao_callback_order_status_dao
        param = business_data.get("param", {})

        # 提取订单状态回调的所有字段
        order_data = {
            "order_id": param.get("order_id"),
            "app_id": param.get("app_id"),
            "partner_order_code": param.get("partner_order_code"),
            "order_status": param.get("order_status"),
            "carrier_driver_id": param.get("carrier_driver_id"),
            "carrier_driver_name": param.get("carrier_driver_name"),
            "carrier_driver_phone": param.get("carrier_driver_phone"),
            "carrier_lat": param.get("carrier_lat"),
            "carrier_lng": param.get("carrier_lng"),
            "description": param.get("description"),
            "detail_description": param.get("detail_description"),
            "error_code": param.get("error_code"),
            "error_scene": param.get("error_scene"),
            "push_time": param.get("push_time"),
            "transfer": param.get("transfer"),
            "api_code": param.get("apiCode"),
            "api_msg": param.get("apiMsg"),
            "complete_pics": param.get("complete_pics"),
            "state": param.get("state"),
            "shipping_order_id": param.get("shipping_order_id"),
            "reverse_shipping_order_id": param.get("reverse_shipping_order_id")
        }

        order_status_record = fengniao_callback_order_status_dao.create_order_status_callback(
            session, callback_id, order_data
        )

        logger.info(f"保存订单状态回调详细信息成功，ID: {order_status_record.id}")
        return True

    except Exception as e:
        logger.error(f"保存订单状态回调详细信息失败: {e}")
        return False


@fengniao_router.post("/fengniao", summary="外部系统回调接口")
async def external_callback(request: Request, session: Session = Depends(get_db)):
    """蜂鸟配送回调接口

    处理蜂鸟配送的各种回调，包括：
    - 订单状态变更回调 (orderStatusNotify)
    - 逆向订单状态回调 (reverseOrderNotify)
    - 门店配送范围变更回调 (chainstoreServiceStatusNotify)
    - 门店状态变更回调 (chainstoreStatusNotify)
    - 门店营业时间变更回调 (chainstoreBusinessTimeNotify)
    - 异常报备回调 (abnormalReportNotify)
    - 商户出餐回调 (cookingFinishNotify)
    """
    try:
        # 获取回调数据
        body = await request.json()

        # 记录原始回调信息到日志
        logger.info(f"[{datetime.now()}] 蜂鸟回调信息: {json.dumps(body, ensure_ascii=False, indent=2)}")

        # 解析business_data
        business_data = _parse_business_data(body.get("business_data", "{}"))
        callback_type = business_data.get("callback_business_type", "unknown")

        logger.info(f"回调类型: {callback_type}")
        logger.info(f"应用ID: {body.get('app_id', 'N/A')}")
        logger.info(f"时间戳: {body.get('timestamp', 'N/A')}")

        # 保存回调数据到数据库
        callback_id = _save_callback_to_db(session, body)

        if callback_id:
            # 根据回调类型进行特殊处理
            if callback_type in ["orderStatusNotify", "reverseOrderNotify"]:
                # 订单状态变更回调，保存详细信息到专门的表
                logger.info("处理订单状态变更回调")
                _save_order_status_callback(session, callback_id, business_data)

                # 打印订单状态信息
                param = business_data.get("param", {})
                logger.info(f"订单号: {param.get('order_id', 'N/A')}")
                logger.info(f"外部订单号: {param.get('partner_order_code', 'N/A')}")
                logger.info(f"订单状态: {param.get('order_status', 'N/A')}")
                logger.info(f"骑手信息: {param.get('carrier_driver_name', 'N/A')} ({param.get('carrier_driver_phone', 'N/A')})")

            elif callback_type == "chainstoreServiceStatusNotify":
                # 门店配送范围变更回调
                logger.info("处理门店配送范围变更回调")
                param = business_data.get("param", {})
                logger.info(f"门店编码: {param.get('out_shop_code', 'N/A')}")
                logger.info(f"回调场景: {param.get('option_type', 'N/A')}")

            elif callback_type == "chainstoreStatusNotify":
                # 门店状态变更回调
                logger.info("处理门店状态变更回调")
                param = business_data.get("param", {})
                logger.info(f"门店ID: {param.get('chain_store_id', 'N/A')}")
                logger.info(f"门店状态: {param.get('status', 'N/A')}")

            else:
                logger.info(f"处理其他类型回调: {callback_type}")

            # 提交数据库事务
            session.commit()
            logger.info("回调处理完成，数据已保存")
        else:
            logger.error("保存回调数据失败")
            session.rollback()

        # 返回成功响应
        return {
            "code": 200,
            "message": "success",
            "data": {
                "callback_type": callback_type,
                "processed_at": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"处理蜂鸟回调失败: {e}", exc_info=True)
        session.rollback()

        # 根据文档，如果处理失败需要返回特定格式让蜂鸟重试
        return {
            "code": 500,
            "message": "处理失败",
            "_fengniao_code_": "500"  # 蜂鸟重试标识
        }
