from pathlib import Path
from typing import List, Union, Optional
import os

from pydantic import AnyHttpUrl, validator, field_validator, PostgresDsn
from pydantic_settings import BaseSettings, SettingsConfigDict
import json
from pydantic import Field


# 根据环境变量选择不同的环境配置文件
ENV = os.getenv("ENV", "stage")
env_file = {
    "stage": ".env.stage",
    "test": ".env.test",
    "prod": ".env"
}.get(ENV, ".env")


class Settings(BaseSettings):
    # 项目基本信息
    PROJECT_NAME: str
    API_V1_STR: str
    CALLBACK_STR: str

    # 安全配置
    SECRET_KEY: str
    ACCESS_TOKEN_EXPIRE_MINUTES: int
    ALGORITHM: str = "HS256"  # JWT 算法

    # 数据库配置
    SQLALCHEMY_DATABASE_URI: str
    DB_LOGGING_ENABLED: bool = False  # 数据库日志开关，默认关闭

    # CORS 配置
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = []

    # 超级管理员配置
    FIRST_SUPERUSER: str = "admin"
    FIRST_SUPERUSER_PASSWORD: str = "admin123"

    @field_validator("BACKEND_CORS_ORIGINS")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 微信小程序配置
    WECHAT_APPID: str
    WECHAT_SECRET: str
    WECHAT_TOKEN: str

    # 微信支付配置
    WXPAY_MCHID: str
    WXPAY_PRIVATE_KEY_PATH: str
    WXPAY_SERIAL_NO: str
    WXPAY_API_V3_KEY: str

    # 文件上传配置
    UPLOAD_DIR: Path
    MAX_UPLOAD_SIZE: int
    ALLOWED_EXTENSIONS: List[str]

    @field_validator("ALLOWED_EXTENSIONS")
    def assemble_allowed_extensions(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)

    # 微信小程序静态文件配置
    UPLOAD_FOLDER: str = "static/uploads"
    MAX_CONTENT_LENGTH: int = 16 * 1024 * 1024  # 16MB

    # 静态文件服务配置
    ENABLE_STATIC_FILES: bool = False  # 是否启用静态文件服务，默认启用

    # 微信小程序域名配置
    BASE_URL: str

    # 小程序前端配置
    RULE_ITEMS_MEAL_TYPE: dict = {
        'BREAKFAST': 0,
        'LUNCH': 1,           # 默认启用午餐
        'AFTERNOON_TEA': 0,
        'DINNER': 1,          # 默认启用晚餐
        'NIGHT_SNACK': 0
    }

    # 个人固定支付金额配置（单位：元）
    PERSONAL_PAYMENT_FIXED_AMOUNT: float = Field(default=3.00, description="个人固定支付金额")

    # 企业自助晚餐开放企业列表（企业名称）
    OPEN_ENTERPRISE_LIST: List[str] = Field(default=[], description="企业自助晚餐开放企业列表")

    @field_validator("RULE_ITEMS_MEAL_TYPE")
    def parse_meal_type_config(cls, v):
        if isinstance(v, str):
            try:
                return json.loads(v)
            except json.JSONDecodeError:
                # 如果解析失败，返回默认配置
                return {
                    'BREAKFAST': 0,
                    'LUNCH': 1,
                    'AFTERNOON_TEA': 0,
                    'DINNER': 1,
                    'NIGHT_SNACK': 0
                }
        elif isinstance(v, dict):
            return v
        else:
            # 如果既不是字符串也不是字典，返回默认配置
            return {
                'BREAKFAST': 0,
                'LUNCH': 1,
                'AFTERNOON_TEA': 0,
                'DINNER': 1,
                'NIGHT_SNACK': 0
            }

    # Redis配置
    REDIS_URL: str
    # 验证码有效期（秒）
    SMS_TIMEOUT: int = 300

    # sms 腾讯短信的配置信息
    SMS_SECRET_ID: str
    SMS_SECRET_KEY: str
    SMS_SMSSDK_APPID: str
    SMS_SIGN_NAME: str
    SMS_TEMPLATE_ID: str

    # 邮件配置
    EMAIL_SMTP_SERVER: str
    EMAIL_SMTP_PORT: int
    EMAIL_USERNAME: str
    EMAIL_PASSWORD: str
    EMAIL_USE_TLS: bool
    EMAIL_SENDER_NAME: str
    EMAIL_SENDER_EMAIL: str

    # 钉钉配置（可选）
    DINGTALK_WEBHOOK_URL: Optional[str] = None
    DINGTALK_SECRET: Optional[str] = None
    DINGTALK_AT_MOBILES: Optional[str] = None
    DINGTALK_AT_USER_IDS: Optional[str] = None
    DINGTALK_IS_AT_ALL: bool = False

    # 微信小程序消息配置（可选）
    WECHAT_TEMPLATE_ID: Optional[str] = None
    WECHAT_MINIAPP_PAGEPATH: Optional[str] = None
    WECHAT_DEFAULT_URL: Optional[str] = None

    # 系统消息配置（可选）
    SYSTEM_MESSAGE_USE_DATABASE: bool = True
    SYSTEM_MESSAGE_DEFAULT_TYPE: str = "system"

    # 通知功能配置
    ENABLE_NOTIFICATION: bool = True  # 是否启用通知功能，默认启用

    FENGNIAO_APP_KEY: str = os.getenv("FENGNIAO_APP_KEY", "*******************")
    FENGNIAO_APP_SECRET: str = os.getenv("FENGNIAO_APP_SECRET", "*******************")
    FENGNIAO_API_URL: str = os.getenv("FENGNIAO_API_URL", "*******************")
    FENGNIAO_MERCHANT_ID: str = os.getenv("FENGNIAO_MERCHANT_ID", "*******************")
    FENGNIAO_SHOP_ID: str = os.getenv("FENGNIAO_SHOP_ID", "*******************")
    FENGNIAO_AUTHORIZATION_CODE: str = os.getenv("FENGNIAO_AUTHORIZATION_CODE", "*******************")

    # 蜂鸟点对点配送坐标配置
    FENGNIAO_TRANSPORT_LONGITUDE: float = Field(default=113.431943, description="蜂鸟取货点经度")
    FENGNIAO_TRANSPORT_LATITUDE: float = Field(default=23.095987, description="蜂鸟取货点纬度")
    FENGNIAO_TRANSPORT_ADDRESS: str = Field(default="取货点地址", description="蜂鸟取货点地址描述")
    FENGNIAO_TRANSPORT_TEL: str = Field(default="13800138000", description="蜂鸟取货点联系电话")

    model_config = SettingsConfigDict(
        case_sensitive=True,
        env_file=env_file,
        env_file_encoding="utf-8"
    )

    # 微信官方域名
    WECHAT_OFFICIAL_DOMAIN: str = "https://api.weixin.qq.com"

settings = Settings()

# 确保上传目录存在
os.makedirs(settings.UPLOAD_FOLDER, exist_ok=True)

print(f"当前环境: {ENV}, 使用配置文件: {env_file}")
