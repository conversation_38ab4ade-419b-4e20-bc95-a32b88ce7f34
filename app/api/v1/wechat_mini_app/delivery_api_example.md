# 外卖订单创建API返回结果示例

## API接口
`POST /api/v1/wechat_mini_app/delivery-order/create`

## 请求参数示例
```json
{
    "order_type": "delivery",
    "items": [
        {
            "dish_id": "1",
            "name": "自助餐（午餐）",
            "price": 22,
            "quantity": 1
        }
    ],
    "total_amount": 12,
    "delivery_address": {
        "id": "1758944318198",
        "name": "潘",
        "phone": "18923092944",
        "province": "广东省",
        "city": "广州市",
        "district": "黄埔区",
        "detail": "黄埔大道906号",
        "isDefault": true,
        "latitude": 23.1291,
        "longitude": 113.3185
    },
    "delivery_time": {
        "id": "2",
        "label": "12:00-15:00",
        "value": "12:00-15:00"
    },
    "delivery_fee": 0,
    "coupon_id": "14,17",
    "coupon_discount": 2.5
}
```

## 成功响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "order_no": "YH202412271234567890",
        "order_id": 12345,
        "payable_amount": 19.5,
        "user_balance": 150.75,
        "fengniao_order_id": "300000211323129144"
    }
}
```

## 错误响应示例

### 1. 用户未登录
```json
{
    "code": 401,
    "message": "未登录",
    "data": null
}
```

### 2. 参数错误
```json
{
    "code": 400,
    "message": "缺少必填参数: delivery_address",
    "data": null
}
```

### 3. 商品数量格式错误
```json
{
    "code": 400,
    "message": "商品数量必须为正整数",
    "data": null
}
```

### 4. 配送地址格式错误
```json
{
    "code": 400,
    "message": "配送地址缺少必填字段: phone",
    "data": null
}
```

### 5. 优惠券验证失败
```json
{
    "code": 400,
    "message": "优惠金额验证失败，预期优惠2.5元，实际优惠2.0元",
    "data": null
}
```

### 6. 蜂鸟下单失败
```json
{
    "code": 500,
    "message": "外卖下单失败: 运单超出配送范围",
    "data": null
}
```

### 7. 系统错误
```json
{
    "code": 500,
    "message": "创建订单失败: 数据库连接异常",
    "data": null
}
```

## 数据字段说明

### 请求参数字段
- `order_type`: 订单类型，固定为"delivery"
- `items`: 商品列表
  - `dish_id`: 商品ID
  - `name`: 商品名称
  - `price`: 商品单价
  - `quantity`: 购买数量
- `total_amount`: 订单总金额
- `delivery_address`: 配送地址信息
  - `name`: 收货人姓名
  - `phone`: 收货人电话
  - `province`: 省份
  - `city`: 城市
  - `district`: 区县
  - `detail`: 详细地址
  - `latitude`: 纬度（可选）
  - `longitude`: 经度（可选）
- `delivery_time`: 配送时间信息
  - `label`: 时间段标签
  - `value`: 时间段值
- `delivery_fee`: 配送费
- `coupon_id`: 优惠券ID字符串（可选）
- `coupon_discount`: 预期优惠金额（可选）

### 响应数据字段
- `order_no`: 本地订单号
- `order_id`: 本地订单ID
- `payable_amount`: 应付金额（扣除优惠后）
- `user_balance`: 用户账户余额
- `fengniao_order_id`: 蜂鸟订单号

## 业务流程说明
1. 验证用户登录状态
2. 验证请求参数完整性
3. 创建本地订单记录
4. 调用蜂鸟API创建外卖订单
5. 更新本地订单的蜂鸟信息
6. 返回订单创建结果

## 注意事项
- 所有金额单位为元，保留2位小数
- 配送地址必须包含收货人姓名、电话和详细地址
- 蜂鸟下单失败时会回滚本地订单
- 优惠券验证失败时会阻止订单创建
- 配送时间为可选参数，不传则创建即时单
