配送异常场景说明：
场景一：蜂鸟开放平台接单成功后，会推单给蜂鸟运力创建运单。蜂鸟运力系统会对运单信息做进一步的校验，如校验不通过会导致创建蜂鸟运单失败，平台订单状态会更新为配送失败。

场景二：蜂鸟运力接单成功后，会派单给骑手进行配送，订单配送过程中出现异常导致未能将该订单成功配送至客户签收时，订单状态会更新为配送失败。
配送失败的订单会通过回调接口返回“订单配送异常码”给商户。

目前蜂鸟开放平台会产生的配送异常场景及对应的建议处理方案如下：
ErrorScene	异常场景描述	建议商户处理办法
ORDER_INFORMATION_INCORRECT	商户推单信息有误，创建蜂鸟运单失败	建议商户查看详细的订单异常原因，检查并修正订单信息后重新推单。如对订单异常原因有疑问可联系对接商务/业务人员协助排查
OUT_OF_DELIVERY_SERVICE	超出配送服务能力，创建蜂鸟运单失败	建议商户推单前校验配送服务能力。 如对当前购买的配送服务能力有疑问可联系对接商务/业务人员咨询
MERCHANT_STORE_ERROR	因商户原因导致无法继续配送	建议联系门店进行沟通，定位问题
CUSTOMER_ERROR	因用户原因导致无法继续配送	建议联系顾客进行沟通，定位问题
CARRIER_ERROR	因运力原因导致无法继续配送	稍后重新发单
ELE_SYSTEM_ERROR	蜂鸟系统异常	稍后重新发单
UNKNOW_ERROR	其他未知错误	稍后重新发单
apiCode	异常场景描述	建议商户处理办法
B0102	运单超出配送范围	超配送范围，暂不支持配送
B0105	运单运力紧张，请稍后重试	运单运力紧张，请稍后重试
B0114	压力平衡关店	压力平衡关店
B0118	运单获取折扣信息失败	运单获取折扣信息失败
B0128	超出发单重量上限	超出发单重量上限
B0120	因配送超时异常，导致无法继续配送	因配送超时异常，导致无法继续配送
B0121	因配送接单异常，导致无法继续配送	因配送接单异常，导致无法继续配送
B0122	因用户退单，导致无法继续配送	因用户原因导致无法继续配送
B0123	商户签约状态异常，联系业务处理	商户签约状态异常，联系业务处理
B0133	因运力原因导致无法继续配送	因运力原因导致无法继续配送
B0124	因用户地址错误，导致无法继续配送	因用户原因导致无法继续配送
B0125	因用户联系不到，导致无法继续配送	因用户联系不到，导致无法继续配送
B0126	超出配送服务能力，蜂鸟接单失败	超出配送服务能力，创建蜂鸟运单失败
B0127	因运力时间来不及，导致无法接单	因运力时间来不及，导致无法接单
B0129	因商户缺货原因，导致无法继续配送	因商户缺货原因，导致无法继续配送
B0130	货物重量异常	货物重量异常
B0131	请求配送过晚，无法呼叫运力	请求配送过晚，无法呼叫运力
B0134	因商户出货原因导致无法继续配送	因商户出货原因导致无法继续配送
补充说明：
由于ErrorCode比较多且杂乱（目前已识别到的约50种），后续也可能会继续增加。建议商户系统侧根据ErrorScene定义订单出现异常后的处理规则即可，无需系统识别ErrorCode，新的协议里面可根据apiCode去识别异常码以及异常描述。

订单出现异常时，平台会通过回调接口返回ErrorScene（异常场景）及详细的ErrorCode（异常原因），含详细的中文描述；参见其他接口->回调接口文档

也可以登录平台官网在管理中心->订单管理->订单详情里查看订单异常的详细原因。

若对接方处理订单回调时发生异常，需要蜂鸟重试时请在回调请求结果中增加_fengniao_code_字段，当这个字段为 500时表示需要蜂鸟重试。
例子：

{"_fengniao_code_":"500"}