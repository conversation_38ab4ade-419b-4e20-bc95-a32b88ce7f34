# 蜂鸟回调接口API返回示例

## 成功处理回调

### 订单状态变更回调成功示例

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "callback_type": "orderStatusNotify",
        "processed_at": "2025-01-24T10:30:00.123456"
    }
}
```

### 门店配送范围变更回调成功示例

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "callback_type": "chainstoreServiceStatusNotify",
        "processed_at": "2025-01-24T10:30:00.123456"
    }
}
```

## 处理失败回调

### 处理异常时的返回示例

```json
{
    "code": 500,
    "message": "处理失败",
    "_fengniao_code_": "500"
}
```

注意：`_fengniao_code_` 字段为 "500" 时表示需要蜂鸟重试。

## 数据库保存示例

### fengniao_callbacks 表数据示例

```json
{
    "id": 1,
    "app_id": "7625XXX37",
    "timestamp": 1610629131295,
    "update_time": "2021-01-14 20:25:31",
    "signature": "XXXX",
    "business_data": {
        "callback_business_type": "orderStatusNotify",
        "param": {
            "app_id": "7625XXX37",
            "carrier_driver_id": null,
            "carrier_driver_name": "",
            "carrier_driver_phone": "",
            "carrier_lat": null,
            "carrier_lng": null,
            "description": "因用户原因导致无法继续配送",
            "detail_description": "用户发起取消",
            "error_code": "USER_CANCEL",
            "error_scene": "CUSTOMER_ERROR",
            "order_id": 100000000220275300,
            "order_status": 5,
            "partner_order_code": "9405228041612191",
            "push_time": 1610629131295
        }
    },
    "callback_type": "orderStatusNotify",
    "raw_data": {
        "app_id": "7625XXX37",
        "signature": "XXXX",
        "timestamp": "1610629131295",
        "business_data": "{\"callback_business_type\":\"orderStatusNotify\",\"param\":{...}}"
    },
    "created_at": "2025-01-24 10:30:00",
    "updated_at": "2025-01-24 10:30:00"
}
```

### fengniao_callbacks_order_status 表数据示例

```json
{
    "id": 1,
    "callback_id": 1,
    "order_id": 100000000220275300,
    "app_id": "7625XXX37",
    "partner_order_code": "9405228041612191",
    "order_status": 5,
    "carrier_driver_id": null,
    "carrier_driver_name": "",
    "carrier_driver_phone": "",
    "carrier_lat": null,
    "carrier_lng": null,
    "description": "因用户原因导致无法继续配送",
    "detail_description": "用户发起取消",
    "error_code": "USER_CANCEL",
    "error_scene": "CUSTOMER_ERROR",
    "push_time": 1610629131295,
    "transfer": 0,
    "api_code": null,
    "api_msg": null,
    "complete_pics": null,
    "state": null,
    "shipping_order_id": null,
    "reverse_shipping_order_id": null,
    "created_at": "2025-01-24 10:30:00",
    "updated_at": "2025-01-24 10:30:00"
}
```

## 日志输出示例

### 订单状态变更回调日志

```
2025-01-24 10:30:00,123 INFO [app.callback.fengniao] [2025-01-24 10:30:00.123456] 蜂鸟回调信息: {
  "app_id": "7625XXX37",
  "signature": "XXXX",
  "timestamp": "1610629131295",
  "business_data": "{\"callback_business_type\":\"orderStatusNotify\",\"param\":{\"app_id\":\"7625XXX37\",\"order_id\":100000000220275300,\"partner_order_code\":\"9405228041612191\",\"order_status\":5,\"description\":\"因用户原因导致无法继续配送\"}}"
}
2025-01-24 10:30:00,124 INFO [app.callback.fengniao] 回调类型: orderStatusNotify
2025-01-24 10:30:00,124 INFO [app.callback.fengniao] 应用ID: 7625XXX37
2025-01-24 10:30:00,124 INFO [app.callback.fengniao] 时间戳: 1610629131295
2025-01-24 10:30:00,125 INFO [app.callback.fengniao] 保存回调记录成功，ID: 1, 类型: orderStatusNotify
2025-01-24 10:30:00,125 INFO [app.callback.fengniao] 处理订单状态变更回调
2025-01-24 10:30:00,126 INFO [app.callback.fengniao] 保存订单状态回调详细信息成功，ID: 1
2025-01-24 10:30:00,126 INFO [app.callback.fengniao] 订单号: 100000000220275300
2025-01-24 10:30:00,126 INFO [app.callback.fengniao] 外部订单号: 9405228041612191
2025-01-24 10:30:00,126 INFO [app.callback.fengniao] 订单状态: 5
2025-01-24 10:30:00,126 INFO [app.callback.fengniao] 骑手信息: N/A (N/A)
2025-01-24 10:30:00,127 INFO [app.callback.fengniao] 回调处理完成，数据已保存
```

### 门店配送范围变更回调日志

```
2025-01-24 10:30:00,123 INFO [app.callback.fengniao] [2025-01-24 10:30:00.123456] 蜂鸟回调信息: {
  "app_id": "7654231944014722107",
  "signature": "60dc8bc45a252bd30aaac92f0e1eb8c32836d189a2babe2aec8822d0845395cd",
  "timestamp": "1663258191001",
  "business_data": "{\"callback_business_type\":\"chainstoreServiceStatusNotify\",\"param\":{\"chain_store_id\":\"212870531\",\"merchant_id\":\"3857579\",\"option_type\":2001,\"out_shop_code\":\"271731382\"}}"
}
2025-01-24 10:30:00,124 INFO [app.callback.fengniao] 回调类型: chainstoreServiceStatusNotify
2025-01-24 10:30:00,124 INFO [app.callback.fengniao] 应用ID: 7654231944014722107
2025-01-24 10:30:00,124 INFO [app.callback.fengniao] 时间戳: 1663258191001
2025-01-24 10:30:00,125 INFO [app.callback.fengniao] 保存回调记录成功，ID: 2, 类型: chainstoreServiceStatusNotify
2025-01-24 10:30:00,125 INFO [app.callback.fengniao] 处理门店配送范围变更回调
2025-01-24 10:30:00,125 INFO [app.callback.fengniao] 门店编码: 271731382
2025-01-24 10:30:00,125 INFO [app.callback.fengniao] 回调场景: 2001
2025-01-24 10:30:00,126 INFO [app.callback.fengniao] 回调处理完成，数据已保存
```
