入参
参数名称	类型	含义	说明	示例
app_id	string	应用id	见官网应用管理	
timestamp	string	时间戳	单位毫秒	
signature	string	签名	见下方签名算法	
business_data	string	业务参数	见下方业务参数	
业务参数：

参数名称	类型	含义	示例
callback_business_type	string	回调业务类型	订单状态回调：orderStatusNotify，
异常报备回调：abnormalReportNotify，
商户出餐回调：cookingFinishNotify，
门店状态变更回调：chainstoreStatusNotify，
门店采购服务变更回调 chainstoreServiceStatusNotify
门店营业时间变更回调 chainstoreBusinessTimeNotify
param	string	回调参数	
示例：

{
    "app_id": "7625XXX37", 
    "signature": "XXXX", 
    "timestamp": "1610629131295", 
    "business_data":
        "{
            \"callback_business_type\":\"orderStatusNotify\",
            \"param\":
                {
                    \"app_id\":\"7625XXX37\",
                    \"carrier_driver_id\":null,
                    \"carrier_driver_name\":\"\",
                    \"carrier_driver_phone\":\"\",
                    \"carrier_lat\":null,
                    \"carrier_lng\":null,
                    \"description\":\"因用户原因导致无法继续配送\",
                    \"detail_description\":\"用户发起取消\",
                    \"error_code\":\"USER_CANCEL\",
                    \"error_scene\":\"CUSTOMER_ERROR\",
                    \"order_id\":100000000220275300,
                    \"order_status\":5,
                    \"partner_order_code\":\"9405228041612191\",
                    \"push_time\":1610629131295
                }
        }"
}
门店状态回调
callback_business_type：chainstoreStatusNotify

回调参数 param

参数名	描述	规则描述	示例
merchant_id	商户id		1234
chain_store_id	门店id	蜂鸟门店id	1
out_shop_code	外部门店编码		20
status	门店认证状态	10-上架审核中,20-正常(已上架),
30-上架审核失败,40-已冻结,50-已下架	
modify_status	门店修改状态	0-无修改,10-资料修改审核中,
20-审核通过,30-审核驳回	
remark	门店认证、修改等驳回时返回原因		基本信息不全
示例：

{
	"signature": "cd9bf2b49feeffc5a58eecb7eb8a865c1697c693aaf79118ab20b448ec666090",
	"app_id": "1327047163140400236",
	"timestamp": "1663277980187",
	"business_data": "{
                    \"callback_business_type\":\"chainstoreStatusNotify\",
                    \"param\":{
                                \"chain_store_id\":213208803,
                                \"merchant_id\":4022819,
                                \"out_shop_code\":\"2209160539351383\",
                                \"remark\":null,
                                \"status\":20
                                }
                     }"
}
门店配送范围变更回调
callback_business_type：chainstoreServiceStatusNotify

门店配送范围变更的场景介绍如下：

1、遇到恶劣天气、运力紧张等特殊情况时，蜂鸟侧会进行压力平衡调控，即自动调整门店配送范围（缩小配送范围，或者关店） 。

2、实际承接门店物流业务时，蜂鸟工作人员会对不合理的门店配送范围进行手动调整。

收到门店配送范围变更通知的回调后，建议再调用门店配送范围查询接口获取最新的门店配送范围。

回调参数param

参数名称	类型	说明	备注
out_shop_code	string	门店编码	
option_type	long	回调场景	1001：开通服务成功 1002：开通服务失败 1003：服务关闭
1004：服务开启 2001：门店配送范围调整
basic_goods	List	基础商品列表	
示例1：

{
	"signature": "60dc8bc45a252bd30aaac92f0e1eb8c32836d189a2babe2aec8822d0845395cd",
	"app_id": "7654231944014722107",
	"timestamp": "1663258191001",
	"business_data": "{
                        \"callback_business_type\":\"chainstoreServiceStatusNotify\",
                        \"param\":{
                        \"chain_store_id\":\"212870531\",
                        \"merchant_id\":\"3857579\",
                        \"option_type\":2001,
                        \"out_shop_code\":\"271731382\"
                        }
                      }"
}
示例2：

{
	"signature": "377c8abcf489a5996bd688be5fde4d6fe80d612aaa4c32d63da7bd859b441a79",
	"app_id": "2508604411570494727",
	"timestamp": "1663257735163",
	"business_data": "{
                    \"callback_business_type\":\"chainstoreServiceStatusNotify\",
                    \"param\":{
                                \"basic_goods\":[30023],
                                \"chain_store_id\":\"213208579\",
                                \"merchant_id\":\"4705987\",
                                \"option_type\":1001,
                                \"out_shop_code\":\"18051138\"
                              }
                    }"
}
订单状态变更回调
callback_business_type：

orderStatusNotify：正向订单状态回调
reverseOrderNotify：逆向订单状态回调
回调参数param

参数名称	类型	说明	示例
order_id	long	订单号	
app_id	string	应用id	
partner_order_code	string	外部订单号	
order_status	int	订单状态	订单生成0，运单生成成功1，20：骑手接单，80：骑手到
店，2：配送中，3：已完成，4：已取消，5：配送异常
carrier_driver_id	long	骑手id	
carrier_lat	string	坐标高德	兼容历史，不要使用，需要坐标正常走查询骑手信息接口
carrier_lng	string	坐标高德	兼容历史，不要使用，需要坐标正常走查询骑手信息接口
carrier_driver_name	string	骑手姓名	
carrier_driver_phone	string	骑手电话	
description	string	描述	
error_code	string	异常code	REJECT_ORDER
error_scene	string	异常描述	系统拒单
detail_description	string	详情描述	
push_time	long	状态推送时间 （毫秒）	
transfer	int	转单标识	转单标识 1 是转单 0非转单
apiCode	string	订单状态回调错误码	
apiMsg	string	订单状态回调错误信息描述	
complete_pics	List	送达照片	图片的url
state	int	逆向运单状态	0：运单创建；40：运单完成 。售中逆向场景使用。
shipping_order_id	long	正向运单ID	售中逆向场景使用。
reverse_shipping_order_id	long	售中逆向运单ID	售中逆向场景使用。
说明：
当骑手转单接单后，也会订单状态回调（order_status：20）

异常报备回调
注：异常报备为骑手报备异常，和订单状态没有直接的关系。骑手报备后仍可完成订单
callback_business_type：abnormalReportNotify

回调参数param

参数名称	类型	说明	示例
order_id	long	订单号	
app_id	string	应用id	
partner_order_code	string	外部订单号	
carrier_driver_id	long	骑手id	
latitude	string	坐标 高德	兼容历史，不要使用，需要坐标正常走查询骑手信息接口
longitude	string	坐标 高德	兼容历史，不要使用，需要坐标正常走查询骑手信息接口
abnormal_code	string	异常报备code	
abnormal_desc	string	异常报备描述	
abnormal_report_time	long	异常报备时间 毫秒	
商户出餐回调
callback_business_type：cookingFinishNotify

回调参数param

参数名称	类型	说明	示例
order_id	long	订单号	
app_id	string	应用id	
partner_order_code	string	外部订单号	
chain_store_id	string	门店id	
merchant_id	string	商户id	
cooking_finish_time	long	出餐时间 （毫秒）	
营业时间变更回调
callback_business_type：chainstoreBusinessTimeNotify

使用场景：当签约商品变更或者运力营业时间变更导致门店营业时间变更时，会实时回调给商户，商户根据回调的结果，去反查门店详情接口，获取到最新的门店营业时间。

回调参数param

参数名称	类型	说明	示例
merchant_id	Long	商户Id	
chain_store_id	Long	蜂鸟门店id	
out_shop_code	String	外部门店编码