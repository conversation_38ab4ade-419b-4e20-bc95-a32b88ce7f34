订单信息同步接口

接口名：standardSyncOrderInfo

业务入参

参数名称	类型	是否必填	说明	备注
order_id	Long	是	订单号	订单号与来源单号必传其一
partner_order_code	String	是	来源单号	
app_id	String	是	应用id	传来源单号时，必传应用id
type	String	是	修改订单信息的类型	
cooking_finish：实际出餐时间
data	Map<String,Object>	是	修改订单信息的内容	
实际出餐时间
key：cookingFinishAt
value：毫秒时间戳
merchant_id	Long	是	商户id	

demo:

{
  "app_id": "5078890923160569248",
  "merchant_id": "201817483",
  "timestamp": "1724160923000",
  "signature": "9733f7af630e180cdb2d801ce6f3da718bbd248f1b499dd1380d789c6e6943ec",
  "access_token": "5b219056-1a6e-45a0-adcb-08858161a4cf",
  "version": "1.0",
  "business_data": "{\"order_id\": 100000136585936474,\"type\": \"cooking_finish\",\"data\": {\"cookingFinishAt\":1727319600000},\"merchant_id\": \"201819731\"}"
}
业务出参

参数名称	类型	是否必填	说明	备注
result	Boolean	是	true时表示推送成功	
reject_code	String	否	同步失败原因码	

demo:
推送成功

{
    "msg": "success",
    "code": "200",
    "apiCode": "00000",
    "success": true,
    "sign": "08bf0780856e366eda2bd27d798eef4860e0c2e09b06c3a8c3fb7b1e499acde5",
    "business_data": "{\"result\":true}"
}
推送失败

{
    "msg": "success",
    "code": "200",
    "apiCode": "00000",
    "success": true,
    "sign": "b656dce050e890f53f618ea8d88c5eb2dedba68400a8bfa8a11dff7edd25d74a",
    "business_data": "{\"reject_code\":\"ORDER_STATUS_INVALID\",\"result\":false}"
}
同步失败原因码

错误码	说明	指引处理
ILLEGAL_ARGUMENT	参数异常	
ORDER_NOT_EXIST	订单不存在	
ORDER_STATUS_INVALID	订单状态无效	
ORDER_OPERATE_PERMISSION_DENIED	订单操作权限被拒绝	
SYSTEM_ERROR	系统异常，请重试