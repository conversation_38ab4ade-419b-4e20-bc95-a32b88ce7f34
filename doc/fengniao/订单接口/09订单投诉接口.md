订单投诉接口
接口名：complaintOrder

业务入参
参数名	类型	是否必须	描述	备注
order_complaint_code	Integer	是	投诉编码	120:配送超时，160:投诉骑手服务态度差, 190:投诉骑手额外索要费用, 170:投诉骑手诱导退单, 140:投诉骑手虚假送达, 210:投诉骑手虚假报备, 200:投诉骑手未配送
partner_order_code	String	否	外部订单号	订单号和外部订单号必填一个
order_complaint_desc	String	否	投诉原因描述	--
order_id	Long	否	订单id	订单号和外部订单号必填一个
file_hashes	List	否	投诉图片	最多支持6张图片
Demo
{
  "app_id": "383965134093979989",
  "merchant_id": "3992099",
  "timestamp": "1625901937042",
  "version": "",
  "business_data": "{\"order_id\": 100000000224257970,\"order_complaint_code\": 160}",
  "signature": "74e9acc7c370db769920649b5a96d94a12a98a34ecf938a914f9480dbe152e37",
  "access_token": "bd947d3f-20ae-416f-9035-9101185a8uio"
}
业务出参
参数名	类型	是否必须	描述	备注
result	Boolean	否	投诉结果	--
Demo
{
  "sign": "946514373028237173fda3c267e08ba9838bec6118b4c8a56adeff9d6c882871",
  "code": "200",
  "msg": "success",
  "business_data": "{\"result\":true}"
}