加小费接口
接口名：addTip
追加小费，订单小费总金额为每次累加
注：不是所有订单都支持加小费，受合同服务 & 接单运力 & 当时订单状态等多个因素影响。 订单详情接口有返回是否可加小费字段。
另：联调环境使用加小费接口 需先使用联调工具->订单状态回调：把订单状态改为1，否则订单状态会不支持加小费

业务入参
参数名	类型	是否必须	描述	备注
partner_order_code	String	否	外部订单号	订单号和外部订单号必填一个
third_index_id	Long	是	本次加小费唯一标识	每个订单内 不可重复， 会用来做幂等，相同third_index_id的后续请求会被忽略
add_tip_amount_cent	Long	是	加小费金额	单位：分
order_id	Long	否	订单id	订单号和外部订单号必填一个
Demo
{
  "app_id": "6605182575524460000",
  "merchant_id": "3868051",
  "timestamp": "1625638138663",
  "signature": "706a03d2a01d185ce2de4341ad7a778285a8a3fe407d0589cb9fe15a97de50de",
  "access_token": "1c76f789-622f-4c08-ab92-143002997989",
  "version": null,
  "business_data": "{\"add_tip_amount_cent\":200,\"order_id\":\"300000215893560503\",\"partner_order_code\":\"7708478YISONG0\",\"third_index_id\":1}",
  "apiUri": "addTip"
}
业务出参
参数名	类型	是否必须	描述	备注
result	Boolean	否	--	--
Demo
{
  "sign": "18fb9ebea14036670b4a6b555b730c436ade703f1f8e2ebb68b06f28cc440e01",
  "code": "200",
  "msg": "success",
  "business_data": "{\"result\":true}",
  "apiCode": "00000"
}
接口错误码
以下为该接口常见返回的错误码
错误码	说明	处理指引
B0101	运单异常，请联系小二反馈	运单异常，请联系小二反馈
B0115	小费支付失败	小费支付失败
B0116	订单不支持加小费	该订单的支付方式或服务商品不支持加小费
A0101	缺少必填参数	请查看文档的必填参数
A0100	参数格式不正确	请查看文档对于参数的说明