订单索赔接口
接口名：claimOrder

业务入参
参数名	类型	是否必须	描述	备注
partner_order_code	String	否	外部订单号	订单号和外部订单号必填一个
order_claim_code	Integer	是	索赔编码	130: 接单后未完成配送,140: 提前点击送达,150: 未保持餐品完整,160:服务态度恶劣,170: 诱导商家/顾客退单,190:额外索要费用,200:虚假配送,210:虚假报备商家/顾客问题,300:配送超时,320:少餐错餐,360:联系顾客取消,370:送错位置,380:丢餐
order_claim_desc	String	否	索赔描述	--
order_claim_sku	List<OrderClaimSkuDto>	否	索赔明细	--
order_claim_price_cent	Integer	是	索赔金额	单位：分
order_id	Long	否	订单id	订单号和外部订单号必填一个
file_hashes	List	否	索赔图片Hash	最多支持6张图片
OrderClaimSkuDto
参数名	类型	是否必须	描述	备注
claim_sku_price_cent	String	否	索赔商品实际单价	单位：分
claim_sku_goods_count	String	否	索赔商品数量	--
claim_sku_name	String	否	索赔商品名称	--
claim_sku_total_price_cent	String	否	索赔商品实际总价	单位：分
Demo
{
  "app_id": "2979251284459979090",
  "merchant_id": "4235547",
  "timestamp": "1625814432323",
  "signature": "7eab916407838621670fdfcbc9efac8126e26514e7e8f0fc21fd7a3a9c48f040",
  "access_token": "119dcb15-7867-43b9-b505-aee60ab24rtg5",
  "version": "1.0",
  "business_data": "{\"partner_order_code\":\"202106291631004760499OI\",\"order_claim_code\":\"130\",\"order_claim_price_cent\":3600}"
}
业务出参
参数名	类型	是否必须	描述	备注
result	Boolean	否	--	--
reverse_order_ok	Boolean	否	--	--
Demo
{
  "sign": "946514373028237173fda3c267e08ba9838bec6118b4c8a56adeff9d6c882871",
  "code": "200",
  "msg": "success",
  "business_data": "{\"result\":true}"
}