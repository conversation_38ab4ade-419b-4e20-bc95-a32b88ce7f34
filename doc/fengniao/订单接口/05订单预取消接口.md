订单预取消接口
接口名：preCancelOrder
获取取消价格，需要查询并校验取消价格时使用，若强校验价格需正式取消接口把出参价格传过去 (见下正式取消接口)

业务入参
参数名	类型	是否必须	描述	备注
partner_order_code	String	否	外部订单号	订单号和外部订单号必填一个
order_cancel_code	Integer	是	取消原因code	从可用取消原因列表接口返回结果选择
order_id	Long	否	订单id	订单号和外部订单号必填一个
Demo
{
  "app_id": "3777515968723875999",
  "merchant_id": "3590107",
  "timestamp": "1626074410391",
  "version": "1.0",
  "business_data": "{\"order_cancel_code\":32,\"order_id\":\"300000219758073736\"}",
  "signature": "ff0ad271c9a32b5248677e9a5f5062cc7b93d1eaddfb3e3a3e1e45f557399b64",
  "access_token": "840c3093-2331-4c4d-bc16-4e4a0cdefg34"
}
业务出参
参数名	类型	是否必须	描述	备注
actual_cancel_cost_cent	Long	否	取消实际需金额	默认使用已有免责取消权益使用后金额为0
Demo
{
  "sign": "86981eb7754e904d4238650757c2b30d8985a211318034d7d4e53cde6708b91f",
  "code": "200",
  "msg": "success",
  "business_data": "{\"actual_cancel_cost_cent\":0}",
  "apiCode": "00000"
}
接口错误码
以下为该接口常见返回的错误码
错误码	说明	处理指引
B0108	运单状态不允许取消	运单状态不允许取消
B0110	运单已取消	运单已取消
B0111	运单不存在	运单不存在
A0100	参数格式不正确	请查看文档对于参数的说明
A0101	缺少必填参数	请查看文档对于参数的说明
00001	蜂鸟系统异常	蜂鸟系统异常，请联系小二解决