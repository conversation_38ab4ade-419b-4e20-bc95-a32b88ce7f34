获取可用订单取消原因接口
接口名：getCancelReasonList

业务入参
参数名	类型	是否必须	描述	备注
partner_order_code	String	否	外部订单号	订单号和外部订单号必填一个
order_id	Long	否	订单id	订单号和外部订单号必填一个
Demo
{
  "app_id": "3777515968723879999",
  "merchant_id": "3590107",
  "timestamp": "1626074410312",
  "version": "1.0",
  "business_data": "{\"order_id\":\"300000219758073736\"}",
  "signature": "70cde7f0fcf2a66c48e24f85f716bd91c4a3ab6930f1078613c9a877b05f0224",
  "access_token": "840c3093-2331-4c4d-bc16-4e4a0cdeased"
}
业务出参
参数名	类型	是否必须	描述	备注
cancel_reason_list	List<CancelReasonDTO>	否	取消原因列表	List CancelReasonDTO见下表
CancelReasonDTO
参数名	类型	是否必须	描述	备注
order_cancel_code	Integer	否	取消原因code	--
order_cancel_desc	String	否	取消原因描述	--
Demo
{
  "sign": "b833a4cbbbc8b70c078985aa805e4fbbe5a0b405597012a3316408a8d98c1bc1",
  "code": "200",
  "msg": "success",
  "business_data": "{\"cancel_reason_list\":[{\"order_cancel_code\":32,\"order_cancel_desc\":\"订单信息填写错误\"},{\"order_cancel_code\":36,\"order_cancel_desc\":\"重复下单了\"},{\"order_cancel_code\":1,\"order_cancel_desc\":\"物流原因：订单长时间未分配骑手\"},{\"order_cancel_code\":4,\"order_cancel_desc\":\"商品缺货/无法出货/已售完\"},{\"order_cancel_code\":6,\"order_cancel_desc\":\"商户发错单\"},{\"order_cancel_code\":7,\"order_cancel_desc\":\"商户/顾客自身定位错误\"},{\"order_cancel_code\":8,\"order_cancel_desc\":\"商户改其他第三方配送\"},{\"order_cancel_code\":9,\"order_cancel_desc\":\"顾客下错单/临时不想要了\"},{\"order_cancel_code\":10,\"order_cancel_desc\":\"顾客自取/不在家/要求另改时间配送\"},{\"order_cancel_code\":0,\"order_cancel_desc\":\"其它（必填原因）\"}]}"
}
接口错误码
以下为该接口常见返回的错误码
错误码	说明	处理指引
B0108	运单异常，请联系小二反馈	运单异常，请联系小二反馈
B0111	运单超出配送范围	超配送范围，暂不支持配送
00001	蜂鸟系统异常	蜂鸟系统异常，请联系小二解决