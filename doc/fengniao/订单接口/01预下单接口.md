预下单接口
接口名：preCreateOrder
返回所有可选的商品列表，每项包含当前使用该商品下单时对应的价格等信息，(其中不可用的商品会返回不可用原因) 可挑选其中一个可用的商品进行正式下单。需强校验价格与入单一致需关注入单校验价格必传字段（见入单接口）非必调 如无需查看和核验下单价格，可直接走下单预下单接口仅支持基于门店发单

业务入参
参数名	类型	是否必须	描述	备注
partner_order_code	String	是	外部订单号	会用来做幂等
use_coupon	Byte	否	是否使用优惠券	0:不使用， 1:使用 默认使用
goods_count	Integer	是	货物件数	--
transport_longitude	Double	否	取货点经度	点对点特殊服务场景才可使用，常规门店发单场景该字段不生效
out_shop_code	String	否	外部门店id	当使用门店发单 out_shop_code和chain_store_id必填1个
service_goods_id	Long	否	服务商品id	有指定需求时使用，也可直接根据返回结果选择(非历史兼容原因无需指定。*不指定指不传或传null，不能传0，0有意义)
receiver_latitude	Double	是	收货人纬度	--
goods_total_amount_cent	Long	是	订单商品总金额	单位：分
transport_tel	String	否	取货点联系人电话	--
order_type	Integer	是	订单类型（1:即时单，3:预约单）	--
chain_store_id	Long	否	门店id	门店id 点对点不传
goods_weight	Double	是	货物总重量	单位：kg
goods_item_list	List<OrderItemOpenapiDto>	是	货物明细	List，OrderItemOpenapiDto见下表
transport_address	String	否	取货点地址	取货点地址描述
goods_actual_amount_cent	Long	是	订单货物实付金额	单位：分
order_tip_amount_cent	Long	否	订单小费金额	单位：分
order_add_time	Long	否	下单时间	单位：毫秒
receiver_longitude	Double	是	收货人经度	--
expect_fetch_time	Long	否	--	--
base_goods_id	Long	否	基础商品id	有指定需求时使用，也可直接根据返回结果选择(非历史兼容原因无需指定。*不指定指不传或传null，不能传0，0有意义)
transport_latitude	Double	否	取货点纬度	点对点特殊服务场景才可使用，常规门店发单场景该字段不生效
order_source	String	否	商户订单来源（如 饿了么、美团等）	手发单/未知来源: 0 或不传 美团:2 口碑:4 饿了么:6 支付宝:7 饿百: 8 抖音:9 京东秒送:10 淘宝闪购ELE:116 淘宝闪购EBAI:118
receiver_address	String	是	收货人地址	--
order_source_order_id	String	否	商户订单来源单号	--
position_source	Integer	是	经纬度来源	坐标经纬度来源（1:腾讯地图,2:百度地图, 3:高德地图），蜂鸟建议使用高德地图
require_receive_time	Long	否	需要送达时间	订单类型为预约单时必传，如需要送达时间 – 推单时间 ＜ 60min，则蜂鸟配送开放平台自动将订单类型置为即时单
appoint_extra_goods_ids	List	否	指定增值商品 id	List列表中 item 为Long
OrderItemOpenapiDto
参数名	类型	是否必须	描述	备注
item_actual_amount_cent	Long	是	商品实际支付金额	单位：分，必须是乘以数量后的金额，否则影响售后环节的赔付标准
item_amount_cent	Long	是	商品原价	单位：分
item_remark	String	否	商品备注	不超过255个字符
item_id	String	否	商品编号	--
item_name	String	否	商品名称(不超过128个字符)	--
item_size	Integer	否	商品尺寸	1:小, 2:中, 3:大
item_quantity	Integer	否	商品数量	--
Demo
{
  "app_id": "6256530608398980900",
  "merchant_id": "101491",
  "timestamp": "1626073090000",
  "version": "1.0",
  "business_data": "{\"partner_order_code\":\"fn-1626073090\",\"chain_store_id\":\"204653075\",\"transport_address\":\"\\u9752\\u5c9b\\u5e02\\u673a\\u7535\\u8bbe\\u5907\\u603b\\u516c\\u53f8 \\u6e29\\u5dde\\u8def44\",\"position_source\":3,\"receiver_address\":\"\\u5174\\u5143\\u8def42\\u53f7\\u697c 8-5-701\",\"receiver_longitude\":\"120.349253\",\"receiver_latitude\":\"36.112728\",\"goods_total_amount_cent\":500,\"goods_actual_amount_cent\":500,\"goods_weight\":\"1\",\"goods_count\":1,\"order_type\":1,\"require_receive_time\":\"\",\"order_remark\":\"\",\"fetch_code\":\"\",\"write_off_code\":\"\",\"customer_ext_tel\":\"\"}",
  "signature": "404f092de87aaafdbb7b8480fe12f28db0d3b0fc47edfcc2d766cb0dad9df883",
  "access_token": "d7d3d7f4-a26e-499b-9abc-5ab429b1xxxx"
}
业务出参
参数名	类型	是否必须	描述	备注
distance	Integer	否	配送距离	单位：米
time	Long	否	预询系统时间戳	单位：毫秒
goods_infos	List<PreCreateOrderOpenpaiGoodsInfo>	否	可用运力列表	goods_infos类型 list服务商品明细见下表
PreCreateOrderOpenpaiGoodsInfo
参数名	类型	是否必须	描述	备注
predict_delivery_time	Long	否	预计送达时间	单位：毫秒
can_add_tip	Integer	否	否支持加小费	0:否 1:是
disable_reason	String	否	不可用原因描述	不可用原因
actual_delivery_amount_cent	Long	否	优惠后配送费总价格	(含入参小费金额) 入单实际价格 关注这一个字段就行!!!
predict_delivery_minutes	Integer	否	预计送达时间	单位：分钟
total_delivery_amount_cent	Long	否	原始配送费总价格(含入参小费金额)	单位：分
price_detail	PriceOpenapiDetail	否	--	--
base_goods_id	Long	否	基础商品id	--
t_index_id	String	否	预询标识	入单时需传入，标识本次预询
service_goods_id	Long	否	服务商品id	--
is_valid	Integer	否	是否可用	0不可用，1可用
slogan	String	否	商品介绍	--
warehouse_id	Long	否	优惠券记录id	--
PriceOpenapiDetail
参数名	类型	是否必须	描述	备注
weight_price_cent	Long	否	重量加价	单位：分
pressure_surcharge_cent	Long	否	运力紧张加价	单位：分
time_period_surcharge_cent	Long	否	时段加价	单位：分
distance_price_cent	Long	否	距离加价	单位：分
river_crossing_surcharge_cent	Long	否	跨江单加价	单位：分
category_surcharge_cent	Long	否	品类加价	单位：分
temporary_surcharge_cent	Long	否	临时加价	单位：分
order_price_surcharge_cent	Long	否	客单价加价	单位：分
start_price_cent	Long	否	起送价	单位：分
Demo
{
  "sign": "cd0fcfd58076bee5d364e68feda88d4c946853e7666b831b9fd0c635a071485f",
  "code": "200",
  "msg": "success",
  "business_data": "{\"distance\":4447,\"goods_infos\":[{\"actual_delivery_amount_cent\":1170,\"base_goods_id\":30024,\"can_add_tip\":0,\"disable_reason\":null,\"is_valid\":1,\"predict_delivery_minutes\":64,\"predict_delivery_time\":null,\"price_detail\":{\"category_surcharge_cent\":0,\"distance_price_cent\":600,\"order_price_surcharge_cent\":0,\"pressure_surcharge_cent\":0,\"river_crossing_surcharge_cent\":0,\"start_price_cent\":570,\"temporary_surcharge_cent\":0,\"time_period_surcharge_cent\":0,\"weight_price_cent\":0},\"service_goods_id\":3008,\"slogan\":\"24小时服务，灵活配送准点达\",\"t_index_id\":\"b69d90c2-b7f0-4f0b-a62c-4ad64aa50835\",\"total_delivery_amount_cent\":1170,\"warehouse_id\":null}],\"remark\":null,\"time\":1626073059352}",
  "apiCode": "00000"
}
接口错误码
以下为该接口常见返回的错误码
错误码	说明	处理指引
B0103	运单预计送达时间不能早于当前时间	运单预计送达时间不能早于当前时间
B0105	运单运力紧张，请稍后重试	运单运力紧张，请稍后重试
B0106	当前区域无运力	当前区域无运力
B0105	运单运力紧张，请稍后重试	运单运力紧张，请稍后重试
B0105	运单运力紧张，请稍后重试	运单运力紧张，请稍后重试
B0112	门店不存在	请登录官网后台查看门店状态信息
B0114	压力平衡关店	压力平衡关店
B0202	门店已冻结	门店已冻结
B0208	商户认证未通过	商户认证未通过，请联系管理员解决
