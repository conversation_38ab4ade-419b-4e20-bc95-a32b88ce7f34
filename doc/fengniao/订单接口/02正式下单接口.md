创建订单接口
接口名：createOrder
若需校验入单配送费价格是否变化，需先走预下单接口，并在下单时填上预下单的信息，此时价格若变更会拒绝入单。

业务入参
参数名	类型	是否必须	描述	备注
partner_order_code	String	是	外部订单号	会用来做幂等
receiver_primary_phone	String	是	收货人主要联系方式	只支持手机号，400开头电话，座机号码以及95013开头、长度13位的虚拟电话；隐私号只支持逗号"," 分隔
actual_delivery_amount_cent	Long	否	优惠后配送费总价格(含入参小费金额) 入单实际配送费价格	不为null则校验配送费价格，不一致拒绝入单 (含入参小费)校验预询配送费价格时 必传
use_coupon	Byte	否	是否使用优惠券	0:不使用， 1:使用 默认使用
goods_count	Integer	是	货物件数	--
transport_longitude	Double	否	取货点经度	点对点特殊服务场景才可使用，常规门店发单场景该字段不生效
out_shop_code	String	否	外部门店id	当使用门店发单 out_shop_code和chain_store_id必填1个
service_goods_id	Long	否	服务商品id	有指定需求时使用，也可直接根据返回结果选择(非历史兼容原因无需指定。*不指定指不传或传null，不能传0，0有意义)
receiver_latitude	Double	是	收货人纬度	--
goods_total_amount_cent	Long	是	订单商品总金额	单位：分
customer_ext_tel	String	否	分机号	--
cancel_code	Integer	否	取消码	（贵品服务必填）签贵品履约才生效，保留字段暂无使用
receiver_name	String	是	收货人姓名	--
transport_tel	String	否	取货点联系人电话	--
pre_create_order_t_index_id	String	否	预询后下单标识	为null不校验配送费价格直接入单 不为null强校验配送费价格 与入参价格不一致拒绝入单
order_type	Integer	是	订单类型（1:即时单，3:预约单）	如果需要送达时间 – 推单时间 ＜ 60min，则蜂鸟配送开放平台自动将订单类型置为即时单，设置预约单无效
chain_store_id	Long	否	门店id	门店id 点对点不传
order_remark	String	否	用户备注	--
goods_weight	Double	是	货物总重量	单位：kg
goods_item_list	List<OrderItemOpenapiDto>	是	货物明细	List，OrderItemOpenapiDto见下表
transport_address	String	否	取货点地址	（1）点对点发单场景必传，骑手依赖此字段取货（2）非点对点场景该字段不生效，取货点取门店地址。
fetch_code	Integer	否	取餐码 骑手和用户交互	签贵品履约才生效
serial_number	String	否	商家订单流水号	商家订单流水号, 方便配送骑手到店取货, 支持数字,字母及#等常见字符. 建议填写。长度<=8。骑手的取货流水号拼接方式：订单来源#流水号
goods_actual_amount_cent	Long	是	订单货物实付金额	单位：分
order_tip_amount_cent	Long	否	订单小费金额	单位：分
order_add_time	Long	否	下单时间	下单时间(毫秒)
receiver_longitude	Double	是	收货人经度	--
expect_fetch_time	Long	否	预计出餐时间	单位：毫秒 预约单该字段必填
base_goods_id	Long	否	基础商品id	有指定需求时使用，也可直接根据返回结果选择(非历史兼容原因无需指定。*不指定指不传或传null，不能传0，0有意义)
transport_latitude	Double	否	取货点纬度	点对点特殊服务场景才可使用，常规门店发单场景该字段不生效
order_source	String	否	商户订单来源（如 饿了么、美团等）	手发单/未知来源: 0 或不传 美团:2 口碑:4 饿了么:6 支付宝:7 饿百: 8 抖音:9 京东秒送:10 淘宝闪购ELE:116 淘宝闪购EBAI:118。骑手的取货流水号拼接方式：订单来源#流水号
write_off_code	Integer	否	核销码	（贵品服务必填）骑手和商户交互 签贵品履约才生效
receiver_address	String	是	收货人地址	收货点文字描述
receiver_second_phone	String	否	收货人备用联系方式	只支持手机号，400开头电话，座机号码以及95013开头、长度13位的虚拟电话
order_source_order_id	String	否	订单来源单号	--
position_source	Integer	是	经纬度来源	坐标经纬度来源（1:腾讯地图,2:百度地图, 3:高德地图），蜂鸟建议使用高德地图
warehouse_id	Long	否	优惠券记录id	--
require_receive_time	Long	否	需要送达时间	这个字段传毫秒级时间戳，不然预约单会被转成即时单
appoint_extra_goods_ids	List	否	指定增值商品 id	List列表中 item 为Long
transport_name	String	否	取货点名称	（1）点对点发单依赖此字段，如不传，将兜底使用商户名称（2）非点对点场景，该字段不生效，取货点取门店名称
OrderItemOpenapiDto
参数名	类型	是否必须	描述	备注
item_actual_amount_cent	Long	是	商品实际支付金额	单位：分，必须是乘以数量后的金额，否则影响售后环节的赔付标准
item_amount_cent	Long	是	商品原价	单位：分
item_remark	String	否	商品备注	不超过255个字符
item_id	String	否	商品编号	--
item_name	String	否	商品名称(不超过128个字符)	--
item_size	Integer	否	商品尺寸	1:小, 2:中, 3:大
item_quantity	Integer	否	商品数量	--
Demo
{
  "app_id": "6189030264748484844",
  "merchant_id": "3599999",
  "timestamp": "1625106208075",
  "signature": "9733f7af630e180cdb2d801ce6f3da718bbd248f1b499dd1380d789c6e6943ec",
  "access_token": "35a1d400-de4e-40ba-9a39-1168e1222222",
  "version": "1.0",
  "business_data": "{\"receiver_primary_phone\":\"***********,4174\",\"goods_actual_amount_cent\":6475,\"out_shop_code\":\"Z3H6\",\"order_remark\":\"【JD】麻烦小哥哥帮忙把东西送到快递柜里面，不要放在其他地方哦谢谢\",\"receiver_address\":\"旌阳区德阳市四川德阳市旌阳区城区凯江路二段32号四川司法警官职业学院(南区)快递柜\",\"goods_item_list\":[{\"item_actual_amount_cent\":895,\"item_amount_cent\":995,\"item_name\":\"切片猪肉脯, 原味\",\"item_id\":\"2008227711103\",\"item_quantity\":1},{\"item_actual_amount_cent\":3290,\"item_amount_cent\":3990,\"item_name\":\"【名创优品】1800mAh手持夹子迷你小风扇（白色）\",\"item_id\":\"2010108110103\",\"item_quantity\":1},{\"item_actual_amount_cent\":895,\"item_amount_cent\":995,\"item_name\":\"海带结（酸辣味）\",\"item_id\":\"2007552410101\",\"item_quantity\":1},{\"item_actual_amount_cent\":895,\"item_amount_cent\":995,\"item_name\":\"炭秘烤肠（奥尔良脆骨肠）\",\"item_id\":\"2007565110104\",\"item_quantity\":1},{\"item_actual_amount_cent\":500,\"item_amount_cent\":500,\"item_name\":\"【名创优品】元気森林 白桃味苏打气泡水（汽水）\",\"item_id\":\"2190421310104\",\"item_quantity\":1}],\"expect_fetch_time\":1625104220000,\"transport_address\":\"四川德阳市旌阳区文庙步行街万花楼一层铺位\",\"receiver_second_phone\":\"***********,4174\",\"goods_weight\":0.94,\"receiver_latitude\":31.12968,\"receiver_name\":\"猪猪\",\"receiver_longitude\":104.41251,\"position_source\":1,\"goods_total_amount_cent\":7475,\"require_receive_time\":1625120420000,\"partner_order_code\":\"2115675722000032\",\"goods_count\":5,\"order_type\":3}"
}
业务出参
参数名	类型	是否必须	描述	备注
order_id	Long	否	订单id	--
Demo
{
  "sign": "6c3e0cb2b76cf733d7b90c38b1095bc925bf14accd9fc513aea34a06ecb07cce",
  "code": "200",
  "msg": "success",
  "business_data": "{\"order_id\":300000211323129144}",
  "apiCode": "00000"
}
接口错误码
以下为该接口常见返回的错误码
错误码	说明	处理指引
B0102	运单超出配送范围	超配送范围，暂不支持配送
B0103	运单预计送达时间不能早于当前时间	运单预计送达时间不能早于当前时间
B0105	运单运力紧张，请稍后重试	运单运力紧张，请稍后重试
B0106	当前区域无运力	当前区域无运力
B0107	资金余额不足	资金余额不足
B0109	运单价格不一致	运单价格不一致
B0114	压力平衡关店	压力平衡关店
B0202	门店已冻结	门店已冻结
B0111	运单不存在	运单不存在