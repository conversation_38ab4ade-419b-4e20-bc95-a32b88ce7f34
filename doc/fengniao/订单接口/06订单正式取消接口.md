订单取消接口
接口名：cancelOrder

业务入参
参数名	类型	是否必须	描述	备注
need_reverse_order	Boolean	否	是否期望发起售中逆向	当订单被取消时,是否期望发起售中逆向
actual_cancel_cost_cent	Long	否	取消价格	单位：分
partner_order_code	String	否	外部订单号	订单号和外部订单号必填一个
order_cancel_role	Integer	是	取消的角色	1商户取消, 2 用户取消
order_cancel_code	Integer	是	取消原因code	--
order_cancel_other_reason	String	是	取消原因补充	20字以内
order_id	Long	否	订单id	订单号和外部订单号必填一个
Demo
{
  "app_id": "3777515968723878989",
  "merchant_id": "3590107",
  "timestamp": "1626074410463",
  "version": "1.0",
  "business_data": "{\"actual_cancel_cost_cent\":0,\"order_cancel_code\":32,\"order_cancel_role\":1,\"order_id\":\"300000219758073736\"}",
  "signature": "977a132d62b126bcc051093fff6ce4c367cecfa669e6711f258ef3c2b06d1fc6",
  "access_token": "840c3093-2331-4c4d-bc16-4e4a0cde34er"
}
业务出参
参数名	类型	是否必须	描述	备注
result	Boolean	否	--	--
reverse_order_ok	Boolean	否	--	--
Demo
{
  "sign": "883fcc96af5c3144d8469c2e1dd09b958f8d771a255197bc10f4da63d0e477d5",
  "code": "200",
  "msg": "success",
  "business_data": "{\"result\":true}",
  "apiCode": "00000"
}
接口错误码
以下为该接口常见返回的错误码
错误码	说明	处理指引
B0108	运单状态不允许取消	运单状态不允许取消
B0110	运单已取消	运单已取消
B0111	运单不存在	运单不存在
B0113	使用免责取消权益失败	使用免责取消权益失败
A0100	参数格式不正确	请查看文档对于参数的说明
A0101	缺少必填参数	请查看文档对于参数的说明
00001	蜂鸟系统异常	蜂鸟系统异常，请联系小二解决