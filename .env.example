# 项目基本信息
PROJECT_NAME=乙禾素食管理系统
API_V1_STR=/api/v1
CALLBACK_STR=/callback

# 安全配置
SECRET_KEY=your-secret-key-here
# 8 days
ACCESS_TOKEN_EXPIRE_MINUTES=11520

# 数据库配置
SQLALCHEMY_DATABASE_URI=mysql+pymysql://root:***.@127.0.0.1/yh_vegan

# CORS 配置
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

# 文件上传配置
UPLOAD_DIR=uploads
# 10MB
MAX_UPLOAD_SIZE=10485760
ALLOWED_EXTENSIONS=["jpg","jpeg","png","gif","pdf","doc","docx"]

# 微信小程序配置
WECHAT_APPID=wxb2********fdd
WECHAT_SECRET=f5e6************************d5d4
WECHAT_TOKEN=0G********fi

# 设置为你的服务器域名
BASE_URL=https://vegan.********.com

# 微信支付配置
# 商户号
WXPAY_MCHID=16********61
# 商户证书序列号
WXPAY_SERIAL_NO=6B********72
# API V3密钥
WXPAY_API_V3_KEY=Mw********DS
# 商户私钥路径
WXPAY_PRIVATE_KEY_PATH=certs/wechat/apiclient_key.pem

# Redis配置
REDIS_URL=redis://:123321@localhost:6379/0
# 验证码有效期（秒）
SMS_TIMEOUT=300

# sms 腾讯短信的配置信息
SMS_SECRET_ID = 'AK**********************TU'
SMS_SECRET_KEY = 'tZ**********************lK'
SMS_SMSSDK_APPID = "14**********************63"
SMS_SIGN_NAME = "深圳******科技"
SMS_TEMPLATE_ID = "18***86"

# 蜂鸟即配配置
FENGNIAO_APP_KEY=your-fengniao-app-key
FENGNIAO_APP_SECRET=your-fengniao-app-secret
FENGNIAO_API_URL=https://open-anubis.ele.me
FENGNIAO_MERCHANT_ID=your-merchant-id
FENGNIAO_SHOP_ID=your-shop-id
FENGNIAO_AUTHORIZATION_CODE=your-authorization-code

# 蜂鸟点对点配送坐标配置
FENGNIAO_TRANSPORT_LONGITUDE=113.431943
FENGNIAO_TRANSPORT_LATITUDE=23.095987
FENGNIAO_TRANSPORT_ADDRESS=取货点地址描述
FENGNIAO_TRANSPORT_TEL=13800138000